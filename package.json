{"name": "arrahmah-handling-service", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@emotion/is-prop-valid": "^1.2.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "html2canvas": "^1.4.1", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "jspdf": "^2.5.1", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.0", "react-i18next": "^14.0.0", "react-quill": "^2.0.0", "react-router-dom": "^6.16.0", "react-share": "^5.1.0", "tailwind-merge": "^1.1.4", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "tailwindcss": "^4.1.12", "terser": "^5.29.0", "vite": "^4.4.5"}}
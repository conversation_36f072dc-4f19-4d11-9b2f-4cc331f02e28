const path = require('path');
const blogPosts = require('./dist/blog-routes.json');

const blogRoutes = blogPosts.map(post => `/blog/${post.slug}`);

module.exports = {
  staticDir: path.join(__dirname, 'dist'),
  routes: [
    '/',
    '/services',
    '/land-arrangement-umrah',
    '/pricing',
    '/about',
    '/contact',
    '/blog',
    '/faq',
    '/testimonials',
    ...blogRoutes,
  ],
  renderer: '@prerenderer/renderer-puppeteer',
  rendererOptions: {
    maxConcurrentRoutes: 5,
    renderAfterElementExists: '#root',
    inject: {},
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  },
  postProcess(renderedRoute) {
    renderedRoute.html = renderedRoute.html
      .replace(/<script (.*?)>/g, '<script $1 defer>')
      .replace('id="root"', 'id="root" data-prerendered="true"');
    return renderedRoute;
  }
};
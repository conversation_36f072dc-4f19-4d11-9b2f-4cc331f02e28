import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster.jsx';
import ScrollToTop from '@/components/layout/ScrollToTop.jsx';
import AnalyticsTracker from '@/components/AnalyticsTracker.jsx';
import { HelmetProvider } from 'react-helmet-async';

import HomePage from '@/pages/HomePage.jsx';
import ServicesPage from '@/pages/ServicesPage.jsx';
import PricingPage from '@/pages/PricingPage.jsx';
import AboutPage from '@/pages/AboutPage.jsx';
import ContactPage from '@/pages/ContactPage.jsx';
import BlogListPage from '@/pages/BlogListPage.jsx';
import BlogPostPage from '@/pages/BlogPostPage.jsx';
import FaqPage from '@/pages/FaqPage.jsx';
import TestimonialsPage from '@/pages/TestimonialsPage.jsx';
import NotFoundPage from '@/pages/NotFoundPage.jsx';
import LandArrangementPage from '@/pages/LandArrangementPage.jsx';
import OrderPage from '@/pages/OrderPage.jsx';
import CustomLaOrderPage from '@/pages/CustomLaOrderPage.jsx';
import PresentationPage from '@/pages/PresentationPage.jsx';
import ProtectedRoute from '@/components/admin/ProtectedRoute.jsx';
import OrderSuccessPage from '@/pages/OrderSuccessPage.jsx';

import AdminLayout from '@/pages/admin/AdminLayout.jsx';
import AdminDashboardPage from '@/pages/admin/AdminDashboardPage.jsx';
import AdminLoginPage from '@/pages/admin/AdminLoginPage.jsx';
import AdminBlogPage from '@/pages/admin/AdminBlogPage.jsx';
import AdminPostEditorPage from '@/pages/admin/AdminPostEditorPage.jsx';
import AdminContentPage from '@/pages/admin/AdminContentPage.jsx';
import AdminMediaPage from '@/pages/admin/AdminMediaPage.jsx';
import AdminLeadsPage from '@/pages/admin/AdminLeadsPage.jsx';
import AdminAnalyticsPage from '@/pages/admin/AdminAnalyticsPage.jsx';
import AdminSettingsPage from '@/pages/admin/AdminSettingsPage.jsx';
import AdminUsersPage from '@/pages/admin/AdminUsersPage.jsx';
import AdminHomeEditorPage from '@/pages/admin/AdminHomeEditorPage.jsx';
import AdminPartnersPage from '@/pages/admin/AdminPartnersPage.jsx';
import AdminLanguageSettingsPage from '@/pages/admin/AdminLanguageSettingsPage.jsx';
import AdminPricingPackagesPage from '@/pages/admin/AdminPricingPackagesPage.jsx';
import AdminMarketingBoosterPage from '@/pages/admin/AdminMarketingBoosterPage.jsx';
import AdminTestimonialsPage from '@/pages/admin/AdminTestimonialsPage.jsx';
import AdminWhatsAppManagementPage from '@/pages/admin/AdminWhatsAppManagementPage.jsx';
import AdminOrdersPage from '@/pages/admin/AdminOrdersPage.jsx';
import AdminOrderDetailPage from '@/pages/admin/AdminOrderDetailPage.jsx';

function App() {

  return (
    <HelmetProvider>
      <ScrollToTop />
      <AnalyticsTracker />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/services" element={<ServicesPage />} />
        <Route path="/land-arrangement-umrah" element={<LandArrangementPage />} />
        <Route path="/pricing" element={<PricingPage />} />
        <Route path="/order" element={<OrderPage />} />
        <Route path="/order/custom-request" element={<CustomLaOrderPage />} />
        <Route path="/order/success/:orderId" element={<OrderSuccessPage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/about/presentation" element={<PresentationPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/blog" element={<BlogListPage />} />
        <Route path="/blog/:slug" element={<BlogPostPage />} />
        <Route path="/faq" element={<FaqPage />} />
        <Route path="/testimonials" element={<TestimonialsPage />} />
        
        <Route path="/admin/login" element={<AdminLoginPage />} />
        <Route 
          path="/admin" 
          element={
            <ProtectedRoute>
              <AdminLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="dashboard" element={<AdminDashboardPage />} />
          <Route path="orders" element={<AdminOrdersPage />} />
          <Route path="orders/:orderId" element={<AdminOrderDetailPage />} />
          <Route path="blog" element={<AdminBlogPage />} />
          <Route path="post-editor" element={<AdminPostEditorPage />} />
          <Route path="post-editor/:postId" element={<AdminPostEditorPage />} />
          <Route path="content" element={<AdminContentPage />} />
          <Route path="home-editor" element={<AdminHomeEditorPage />} />
          <Route path="partners" element={<AdminPartnersPage />} />
          <Route path="pricing-packages" element={<AdminPricingPackagesPage />} />
          <Route path="testimonials" element={<AdminTestimonialsPage />} />
          <Route path="marketing-booster" element={<AdminMarketingBoosterPage />} />
          <Route path="whatsapp-settings" element={<AdminWhatsAppManagementPage />} />
          <Route path="media" element={<AdminMediaPage />} />
          <Route path="leads" element={<AdminLeadsPage />} />
          <Route path="analytics" element={<AdminAnalyticsPage />} />
          <Route path="settings" element={<AdminSettingsPage />} />
          <Route path="language-settings" element={<AdminLanguageSettingsPage />} />
          <Route path="users" element={<AdminUsersPage />} />
        </Route>

        <Route path="*" element={<NotFoundPage />} />
      </Routes>
      <Toaster />
    </HelmetProvider>
  );
}

export default App;
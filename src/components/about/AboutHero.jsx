import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext';

const AboutHero = () => {
  const { translations } = useContext(LanguageContext);

  return (
    <section className="relative py-32 md:py-48 bg-linear-to-br from-secondary via-background to-secondary text-white text-center overflow-hidden">
      <div className="absolute inset-0 z-0 opacity-20">
        <img  
          alt="Abstract geometric Islamic pattern" 
          className="w-full h-full object-cover"
         src="https://images.unsplash.com/photo-1686559020371-779ce33bf9ee" />
      </div>
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.h1
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 leading-tight tracking-tight gold-text-hero"
        >
          {translations.aboutPageTitle || "Mengenal Kami"}
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3, type: "spring", stiffness: 100 }}
          className="text-lg md:text-xl lg:text-2xl mb-10 max-w-3xl mx-auto text-gray-300 leading-relaxed"
        >
          {translations.aboutPageSubtitle || "Mengenal Lebih Dekat Komitmen dan Profesionalisme Kami dalam Melayani Tamu Allah."}
        </motion.p>
      </div>
      <motion.div
        className="absolute bottom-8 left-1/2 -translate-x-1/2"
        animate={{ y: [0, -10, 0] }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white opacity-70 h-8 w-8">
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      </motion.div>
    </section>
  );
};

export default AboutHero;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { BookOpen } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const OurStory = () => {
  const { translations } = useContext(LanguageContext);

  return (
    <section className="py-20 bg-background text-white">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <BookOpen className="w-16 h-16 mx-auto mb-6 text-[#FFD700]" />
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
            {translations.ourStoryTitle || "Cerita Kami: <PERSON><PERSON>"}
          </h2>
          <div className="w-24 h-1.5 bg-linear-to-r from-[#FFD700] to-[#FFA500] mx-auto rounded-full"></div>
        </motion.div>

        <div className="max-w-3xl mx-auto space-y-8 text-lg text-gray-300 leading-relaxed">
          <motion.p
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.ourStoryDesc1 || "Arrahmah Handling Service lahir dari visi untuk memberikan layanan terbaik bagi para tamu Allah. Dengan pengalaman bertahun-tahun di industri perjalanan haji dan umrah, kami memahami betul kompleksitas dan tantangan yang dihadapi oleh travel agent maupun jamaah secara langsung."}
          </motion.p>
          <motion.p
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="pl-0 md:pl-8 border-l-0 md:border-l-4 border-[#FFD700] py-2"
          >
            {translations.ourStoryDesc2 || "Berbasis di Makkah Al-Mukarramah, kami memiliki keunggulan dalam pemahaman lokal, aksesibilitas, serta jaringan yang kuat dengan berbagai pihak terkait. Dukungan dari Muassasah resmi menjadi landasan legalitas dan profesionalisme kami dalam setiap layanan yang diberikan."}
          </motion.p>
          <motion.p
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {translations.ourStoryDesc3 || "Kami percaya bahwa setiap jamaah berhak mendapatkan pengalaman ibadah yang khusyuk, aman, dan nyaman. Oleh karena itu, kami terus berinovasi, mengintegrasikan teknologi, dan meningkatkan kualitas sumber daya manusia untuk memastikan setiap aspek perjalanan Anda tertangani dengan baik."}
          </motion.p>
        </div>
      </div>
    </section>
  );
};

export default OurStory;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Shield, Users, Zap, HeartHandshake as Handshake } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const OurValues = () => {
  const { translations } = useContext(LanguageContext);

  const values = [
    { icon: <Shield className="w-10 h-10 text-[#FFD700]" />, titleKey: 'valueAmanahTitle', descriptionKey: 'valueAmanahDesc' },
    { icon: <Users className="w-10 h-10 text-[#FFD700]" />, titleKey: 'valueProfesionalTitle', descriptionKey: 'valueProfesionalDesc' },
    { icon: <Zap className="w-10 h-10 text-[#FFD700]" />, titleKey: 'valueInovatifTitle', descriptionKey: 'valueInovatifDesc' },
    { icon: <Handshake className="w-10 h-10 text-[#FFD700]" />, titleKey: 'valueKolaboratifTitle', descriptionKey: 'valueKolaboratifDesc' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { type: "spring", stiffness: 100, damping: 12 }
    },
  };

  return (
    <section className="py-20 bg-secondary text-white">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
            {translations.ourValuesTitle || "Nilai-Nilai Kami"}
          </h2>
          <div className="w-24 h-1.5 bg-linear-to-r from-[#FFD700] to-[#FFA500] mx-auto rounded-full"></div>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {values.map((value, index) => (
            <motion.div
              key={index}
              className="bg-linear-to-br from-gray-800 to-gray-900 p-8 rounded-xl shadow-2xl border border-gray-700/50 text-center transition-all duration-300 ease-in-out hover:shadow-glow hover:border-[#FFD700]/50 transform hover:-translate-y-2"
              variants={itemVariants}
            >
              <div className="flex justify-center items-center mb-6">
                {value.icon}
              </div>
              <h3 className="text-2xl font-bold text-white mb-3">
                {translations[value.titleKey] || value.titleKey.replace('value', '').replace('Title', '')}
              </h3>
              <p className="text-gray-400 leading-relaxed text-sm">
                {translations[value.descriptionKey] || value.descriptionKey.replace('value', '').replace('Desc', '')}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default OurValues;
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Button } from '@/components/ui/button.jsx';
import { CalendarDays, Tag, Settings2, FileImage as ImageIcon, Trash2 } from 'lucide-react';
import SimpleRichTextEditor from './editor/SimpleRichTextEditor.jsx';

const PostEditorForm = ({
  postData,
  onInputChange,
  onMetaChange,
  onTitleChange,
  onSlugChange,
  onImageUpload,
  onRemoveImage,
  categories
}) => {
  return (
    <>
      <div className="lg:col-span-8 space-y-6">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardContent className="p-6 space-y-6">
            <div>
              <Label htmlFor="title" className="text-gray-300 sr-only">Judul Post</Label>
              <Input 
                id="title" 
                value={postData.title} 
                onChange={onTitleChange} 
                required 
                placeholder="Masukkan Judul Artikel di Sini" 
                className="bg-transparent border-none text-3xl font-bold h-auto p-0 focus-visible:ring-0 placeholder:text-gray-500" 
              />
            </div>
            <div>
              <Label htmlFor="content" className="text-gray-300 mb-2 block">Isi Artikel</Label>
              <SimpleRichTextEditor
                value={postData.content}
                onChange={(value) => onInputChange('content', value)}
                placeholder="Mulai menulis konten blog Anda di sini..."
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-4 space-y-6">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Settings2 size={18} className="mr-2 text-primary" /> Pengaturan Publikasi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="status" className="text-gray-300">Status</Label>
              <Select value={postData.status} onValueChange={(value) => onInputChange('status', value)}>
                <SelectTrigger className="bg-gray-700 border-gray-600 focus:border-primary">
                  <SelectValue placeholder="Pilih status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 text-white border-gray-700">
                  <SelectItem value="Draft" className="hover:bg-primary/20 focus:bg-primary/20">
                    📝 Draft
                  </SelectItem>
                  <SelectItem value="Published" className="hover:bg-primary/20 focus:bg-primary/20">
                    🌐 Publish
                  </SelectItem>
                  <SelectItem value="Scheduled" className="hover:bg-primary/20 focus:bg-primary/20">
                    ⏰ Jadwalkan
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            {postData.status === 'Scheduled' && (
              <div>
                <Label htmlFor="publish_date" className="text-gray-300 flex items-center">
                  <CalendarDays size={16} className="mr-1.5 text-primary" /> Tanggal Terbit
                </Label>
                <Input 
                  id="publish_date" 
                  type="datetime-local" 
                  value={postData.publish_date} 
                  onChange={(e) => onInputChange('publish_date', e.target.value)} 
                  className="bg-gray-700 border-gray-600 focus:border-primary" 
                />
              </div>
            )}
            <div>
              <Label htmlFor="slug" className="text-gray-300">Slug URL</Label>
              <Input 
                id="slug" 
                value={postData.slug} 
                onChange={onSlugChange} 
                required 
                className="bg-gray-700 border-gray-600 focus:border-primary" 
                placeholder="url-artikel-anda"
              />
              <p className="text-xs text-gray-500 mt-1">
                URL: /blog/{postData.slug || 'url-artikel-anda'}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Tag size={18} className="mr-2 text-primary" /> Kategori & Tag
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="category" className="text-gray-300">Kategori</Label>
              <Select value={postData.category} onValueChange={(value) => onInputChange('category', value)}>
                <SelectTrigger className="bg-gray-700 border-gray-600 focus:border-primary">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 text-white border-gray-700">
                  {categories.map(cat => (
                    <SelectItem key={cat} value={cat} className="hover:bg-primary/20 focus:bg-primary/20">
                      {cat}
                    </SelectItem>
                  ))}
                  <SelectItem value="" className="text-gray-400 italic hover:bg-primary/20 focus:bg-primary/20">
                    Tidak ada kategori
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="tags" className="text-gray-300">Tag (pisahkan dengan koma)</Label>
              <Input 
                id="tags" 
                value={postData.tags} 
                onChange={(e) => onInputChange('tags', e.target.value)} 
                className="bg-gray-700 border-gray-600 focus:border-primary"
                placeholder="umrah, travel, tips"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <ImageIcon size={18} className="mr-2 text-primary" /> Gambar Utama
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Input 
              id="featuredImageUpload" 
              type="file" 
              accept="image/*" 
              onChange={onImageUpload} 
              className="bg-gray-700 border-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-black hover:file:bg-primary/90" 
            />
            {postData.featured_image && (
              <div className="mt-2 relative">
                <img 
                  src={postData.featured_image} 
                  alt="Preview Gambar Utama" 
                  className="rounded-md max-h-48 w-full object-cover" 
                />
                <Button 
                  type="button" 
                  variant="destructive" 
                  size="icon" 
                  className="absolute top-2 right-2 h-7 w-7" 
                  onClick={onRemoveImage}
                >
                  <Trash2 size={16} />
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              🔍 Optimasi SEO
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="metaTitle" className="text-gray-300 flex justify-between">
                <span>Meta Title</span>
                <span className={postData.meta.title.length > 60 ? 'text-red-400' : 'text-gray-400'}>
                  {postData.meta.title.length}/60
                </span>
              </Label>
              <Input 
                id="metaTitle" 
                value={postData.meta.title} 
                onChange={(e) => onMetaChange('title', e.target.value.slice(0, 60))} 
                className="bg-gray-700 border-gray-600 focus:border-primary" 
                placeholder="Judul untuk mesin pencari"
              />
            </div>
            <div>
              <Label htmlFor="metaDescription" className="text-gray-300 flex justify-between">
                <span>Meta Description</span>
                <span className={postData.meta.description.length > 160 ? 'text-red-400' : 'text-gray-400'}>
                  {postData.meta.description.length}/160
                </span>
              </Label>
              <Textarea 
                id="metaDescription" 
                value={postData.meta.description} 
                onChange={(e) => onMetaChange('description', e.target.value.slice(0, 160))} 
                rows={4} 
                className="bg-gray-700 border-gray-600 focus:border-primary" 
                placeholder="Deskripsi singkat untuk mesin pencari"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default PostEditorForm;
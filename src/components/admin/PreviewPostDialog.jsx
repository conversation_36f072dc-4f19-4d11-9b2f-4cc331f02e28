import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog.jsx';
import { ScrollArea } from '@/components/ui/scroll-area.jsx';
import { Separator } from '@/components/ui/separator.jsx';
import { CalendarDays, UserCircle, Tag } from 'lucide-react';

const PreviewPostDialog = ({ isOpen, onClose, postData }) => {
  if (!postData) return null;

  const placeholderImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg";

  const formatDate = (dateString) => {
    if (!dateString) return 'Tanggal tidak tersedia';
    try {
      return new Date(dateString).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Format tanggal tidak valid';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle className="text-2xl text-primary">Pratinjau Artikel</DialogTitle>
          <DialogDescription className="text-gray-400">
            Ini adalah tampilan artikel Anda sebelum dipublikasikan.
          </DialogDescription>
        </DialogHeader>
        <Separator className="my-4 bg-gray-700" />
        <ScrollArea className="max-h-[70vh] pr-6">
          <div className="space-y-6">
            <header className="space-y-2">
              <h1 className="text-4xl font-extrabold text-white leading-tight">
                {postData.title || 'Judul Artikel Anda'}
              </h1>
              <div className="flex flex-wrap items-center text-gray-400 text-sm space-x-4">
                <span className="flex items-center">
                  <CalendarDays size={16} className="mr-1.5 text-primary" />
                  {formatDate(postData.publish_date)}
                </span>
                <span className="flex items-center">
                  <UserCircle size={16} className="mr-1.5 text-primary" />
                  {postData.author || 'Author'}
                </span>
                {postData.category && (
                  <span className="flex items-center">
                    <Tag size={16} className="mr-1.5 text-primary" />
                    {postData.category}
                  </span>
                )}
              </div>
            </header>

            <div className="my-6 rounded-lg overflow-hidden aspect-video">
              <img src={postData.featured_image || placeholderImage} alt="Featured" className="w-full h-full object-cover" />
            </div>

            <div
              className="prose prose-lg prose-invert max-w-none text-gray-300"
              dangerouslySetInnerHTML={{ __html: postData.content || '<p>Konten artikel akan ditampilkan di sini...</p>' }}
            />
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default PreviewPostDialog;
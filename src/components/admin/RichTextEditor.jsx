import React from 'react';
import ReactQuill from 'react-quill';

const modules = {
  toolbar: [
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    ['blockquote', 'code-block'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'script': 'sub'}, { 'script': 'super' }],
    [{ 'indent': '-1'}, { 'indent': '+1' }],
    [{ 'direction': 'rtl' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'font': [] }],
    [{ 'align': [] }],
    ['link', 'image', 'video'],
    ['clean']
  ],
};

const formats = [
  'header',
  'bold', 'italic', 'underline', 'strike', 'blockquote', 'code-block',
  'list', 'bullet', 'script', 'indent', 'direction',
  'color', 'background', 'font', 'align',
  'link', 'image', 'video'
];

const RichTextEditor = ({ value, onChange, placeholder }) => {
  return (
    <ReactQuill 
      theme="snow" 
      value={value} 
      onChange={onChange}
      modules={modules}
      formats={formats}
      placeholder={placeholder || "Mulai menulis..."}
      className="quill-editor-override"
    />
  );
};

export default RichTextEditor;
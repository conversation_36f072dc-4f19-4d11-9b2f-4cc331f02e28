import React from 'react';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { FileText, Eye, Edit, TrendingUp } from 'lucide-react';

const BlogStatistics = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card className="bg-gray-800/50 border-gray-700/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Artikel</p>
              <p className="text-2xl font-bold text-white">{stats.total}</p>
            </div>
            <FileText className="h-8 w-8 text-primary" />
          </div>
        </CardContent>
      </Card>
      <Card className="bg-gray-800/50 border-gray-700/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Dipublikasi</p>
              <p className="text-2xl font-bold text-green-400">{stats.published}</p>
            </div>
            <Eye className="h-8 w-8 text-green-400" />
          </div>
        </CardContent>
      </Card>
      <Card className="bg-gray-800/50 border-gray-700/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Draft</p>
              <p className="text-2xl font-bold text-yellow-400">{stats.draft}</p>
            </div>
            <Edit className="h-8 w-8 text-yellow-400" />
          </div>
        </CardContent>
      </Card>
      <Card className="bg-gray-800/50 border-gray-700/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Views</p>
              <p className="text-2xl font-bold text-blue-400">{stats.totalViews}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-blue-400" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BlogStatistics;
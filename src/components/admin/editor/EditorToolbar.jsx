import React from 'react';
import { Button } from '@/components/ui/button.jsx';
import { 
  Bold, 
  Italic, 
  Underline, 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  List, 
  ListOrdered, 
  Quote, 
  Link, 
  Image, 
  Code,
  Heading1,
  Heading2,
  Heading3,
  Type
} from 'lucide-react';

const EditorToolbar = ({ onFormat, activeFormats = [] }) => {
  const isActive = (format) => activeFormats.includes(format);

  const toolbarButtons = [
    {
      icon: Heading1,
      action: () => onFormat('h1'),
      isActive: () => isActive('h1'),
      title: 'Heading 1'
    },
    {
      icon: Heading2,
      action: () => onFormat('h2'),
      isActive: () => isActive('h2'),
      title: 'Heading 2'
    },
    {
      icon: Heading3,
      action: () => onFormat('h3'),
      isActive: () => isActive('h3'),
      title: 'Heading 3'
    },
    {
      icon: Type,
      action: () => onFormat('p'),
      isActive: () => isActive('p'),
      title: 'Paragraph'
    },
    { type: 'separator' },
    {
      icon: Bold,
      action: () => onFormat('bold'),
      isActive: () => isActive('bold'),
      title: 'Bold'
    },
    {
      icon: Italic,
      action: () => onFormat('italic'),
      isActive: () => isActive('italic'),
      title: 'Italic'
    },
    {
      icon: Underline,
      action: () => onFormat('underline'),
      isActive: () => isActive('underline'),
      title: 'Underline'
    },
    { type: 'separator' },
    {
      icon: AlignLeft,
      action: () => onFormat('alignLeft'),
      isActive: () => isActive('alignLeft'),
      title: 'Align Left'
    },
    {
      icon: AlignCenter,
      action: () => onFormat('alignCenter'),
      isActive: () => isActive('alignCenter'),
      title: 'Align Center'
    },
    {
      icon: AlignRight,
      action: () => onFormat('alignRight'),
      isActive: () => isActive('alignRight'),
      title: 'Align Right'
    },
    { type: 'separator' },
    {
      icon: List,
      action: () => onFormat('bulletList'),
      isActive: () => isActive('bulletList'),
      title: 'Bullet List'
    },
    {
      icon: ListOrdered,
      action: () => onFormat('orderedList'),
      isActive: () => isActive('orderedList'),
      title: 'Ordered List'
    },
    {
      icon: Quote,
      action: () => onFormat('blockquote'),
      isActive: () => isActive('blockquote'),
      title: 'Quote'
    },
    { type: 'separator' },
    {
      icon: Link,
      action: () => onFormat('link'),
      isActive: () => isActive('link'),
      title: 'Link'
    },
    {
      icon: Image,
      action: () => onFormat('image'),
      isActive: () => isActive('image'),
      title: 'Image'
    },
    {
      icon: Code,
      action: () => onFormat('code'),
      isActive: () => isActive('code'),
      title: 'Code'
    }
  ];

  return (
    <div className="flex flex-wrap items-center gap-1 p-3 border-b border-gray-600 bg-gray-800 rounded-t-md">
      {toolbarButtons.map((button, index) => {
        if (button.type === 'separator') {
          return <div key={index} className="w-px h-6 bg-gray-600 mx-1" />;
        }

        const Icon = button.icon;
        return (
          <Button
            key={index}
            variant="ghost"
            size="sm"
            onClick={button.action}
            className={`h-8 w-8 p-0 ${
              button.isActive() 
                ? 'bg-primary text-black hover:bg-primary/90' 
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            }`}
            title={button.title}
          >
            <Icon size={16} />
          </Button>
        );
      })}
    </div>
  );
};

export default EditorToolbar;
import React, { useState, useRef, useCallback } from 'react';
import EditorToolbar from './EditorToolbar.jsx';

const SimpleRichTextEditor = ({ value, onChange, placeholder }) => {
  const editorRef = useRef(null);
  const [activeFormats, setActiveFormats] = useState([]);

  const handleFormat = useCallback((command) => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();

    switch (command) {
      case 'h1':
        document.execCommand('formatBlock', false, '<h1>');
        break;
      case 'h2':
        document.execCommand('formatBlock', false, '<h2>');
        break;
      case 'h3':
        document.execCommand('formatBlock', false, '<h3>');
        break;
      case 'p':
        document.execCommand('formatBlock', false, '<p>');
        break;
      case 'bold':
        document.execCommand('bold', false, null);
        break;
      case 'italic':
        document.execCommand('italic', false, null);
        break;
      case 'underline':
        document.execCommand('underline', false, null);
        break;
      case 'alignLeft':
        document.execCommand('justifyLeft', false, null);
        break;
      case 'alignCenter':
        document.execCommand('justifyCenter', false, null);
        break;
      case 'alignRight':
        document.execCommand('justifyRight', false, null);
        break;
      case 'bulletList':
        document.execCommand('insertUnorderedList', false, null);
        break;
      case 'orderedList':
        document.execCommand('insertOrderedList', false, null);
        break;
      case 'blockquote':
        document.execCommand('formatBlock', false, '<blockquote>');
        break;
      case 'link':
        const url = prompt('Masukkan URL:');
        if (url) {
          document.execCommand('createLink', false, url);
        }
        break;
      case 'image':
        const imageUrl = prompt('Masukkan URL gambar:');
        if (imageUrl) {
          document.execCommand('insertImage', false, imageUrl);
        }
        break;
      case 'code':
        document.execCommand('formatBlock', false, '<pre>');
        break;
      default:
        break;
    }

    updateActiveFormats();
  }, []);

  const updateActiveFormats = useCallback(() => {
    const formats = [];
    
    if (document.queryCommandState('bold')) formats.push('bold');
    if (document.queryCommandState('italic')) formats.push('italic');
    if (document.queryCommandState('underline')) formats.push('underline');
    
    setActiveFormats(formats);
  }, []);

  const handleInput = useCallback(() => {
    const editor = editorRef.current;
    if (editor && onChange) {
      onChange(editor.innerHTML);
    }
    updateActiveFormats();
  }, [onChange, updateActiveFormats]);

  const handleKeyDown = useCallback((e) => {
    // Handle common keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          handleFormat('bold');
          break;
        case 'i':
          e.preventDefault();
          handleFormat('italic');
          break;
        case 'u':
          e.preventDefault();
          handleFormat('underline');
          break;
        default:
          break;
      }
    }
  }, [handleFormat]);

  return (
    <div className="border border-gray-600 rounded-md overflow-hidden bg-gray-700">
      <EditorToolbar onFormat={handleFormat} activeFormats={activeFormats} />
      <div
        ref={editorRef}
        contentEditable
        className="min-h-[400px] p-4 bg-gray-700 text-gray-200 focus:outline-hidden prose prose-lg prose-invert max-w-none"
        style={{ 
          lineHeight: '1.6',
          fontSize: '16px'
        }}
        dangerouslySetInnerHTML={{ __html: value || '' }}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        onMouseUp={updateActiveFormats}
        onKeyUp={updateActiveFormats}
        data-placeholder={placeholder || "Mulai menulis konten Anda di sini..."}
      />
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9CA3AF;
          pointer-events: none;
          position: absolute;
        }
        [contenteditable] h1 {
          font-size: 2rem;
          font-weight: bold;
          margin: 1rem 0;
          color: #FFD700;
        }
        [contenteditable] h2 {
          font-size: 1.5rem;
          font-weight: bold;
          margin: 0.875rem 0;
          color: #FFD700;
        }
        [contenteditable] h3 {
          font-size: 1.25rem;
          font-weight: bold;
          margin: 0.75rem 0;
          color: #FFD700;
        }
        [contenteditable] p {
          margin: 1rem 0;
          line-height: 1.7;
        }
        [contenteditable] blockquote {
          border-left: 4px solid #FFD700;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: #FFD700;
        }
        [contenteditable] ul, [contenteditable] ol {
          margin: 1rem 0;
          padding-left: 2rem;
        }
        [contenteditable] li {
          margin: 0.5rem 0;
        }
        [contenteditable] pre {
          background-color: #374151;
          padding: 1rem;
          border-radius: 0.375rem;
          margin: 1rem 0;
          overflow-x: auto;
        }
        [contenteditable] a {
          color: #FFD700;
          text-decoration: underline;
        }
        [contenteditable] img {
          max-width: 100%;
          height: auto;
          border-radius: 0.375rem;
          margin: 1rem 0;
        }
      `}</style>
    </div>
  );
};

export default SimpleRichTextEditor;
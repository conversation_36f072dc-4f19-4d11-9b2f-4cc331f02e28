import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter as DialogPrimitiveFooter } from "@/components/ui/dialog.jsx";
import { Save, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast.js';

const MessageEditorTab = ({
  campaignMode, setCampaignMode,
  messageTitle, setMessageTitle,
  messageContent, setMessageContent,
  templates, setTemplates,
  selectedTemplate, setSelectedTemplate
}) => {
  const { toast } = useToast();
  const [newTemplateName, setNewTemplateName] = useState('');
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);

  const handleSaveTemplate = () => {
    if (!newTemplateName || !messageContent) {
        toast({title: "Data Tidak Lengkap", description: "Nama template dan isi pesan wajib diisi.", variant: "destructive"});
        return;
    }
    const newTemplate = { name: newTemplateName, title: messageTitle, content: messageContent };
    const updatedTemplates = [...templates, newTemplate];
    setTemplates(updatedTemplates);
    localStorage.setItem('messageTemplates', JSON.stringify(updatedTemplates));
    toast({title: "Template Disimpan", description: `Template "${newTemplateName}" berhasil disimpan.`});
    setNewTemplateName('');
    setIsTemplateModalOpen(false);
  };

  const handleLoadTemplate = (templateName) => {
    const template = templates.find(t => t.name === templateName);
    if (template) {
      setMessageTitle(template.title || '');
      setMessageContent(template.content || '');
      setSelectedTemplate(templateName);
      toast({title: "Template Dimuat", description: `Template "${templateName}" berhasil dimuat.`});
    }
  };
  
  const handleDeleteTemplate = (templateName) => {
    const updatedTemplates = templates.filter(t => t.name !== templateName);
    setTemplates(updatedTemplates);
    localStorage.setItem('messageTemplates', JSON.stringify(updatedTemplates));
    if(selectedTemplate === templateName) setSelectedTemplate('');
    toast({title: "Template Dihapus", description: `Template "${templateName}" telah dihapus.`});
  };

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-xl text-primary">Buat atau Edit Pesan Kampanye</CardTitle>
         <div className="flex items-center space-x-2 mt-2">
          <Label className="text-gray-300">Mode Kampanye:</Label>
          <Select value={campaignMode} onValueChange={setCampaignMode}>
              <SelectTrigger className="w-[180px] bg-gray-700 border-gray-600"><SelectValue /></SelectTrigger>
              <SelectContent className="bg-gray-800 text-white border-gray-700">
                  <SelectItem value="email" className="hover:bg-primary/20">Email Campaign</SelectItem>
                  <SelectItem value="whatsapp" className="hover:bg-primary/20">WhatsApp Broadcast</SelectItem>
              </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {campaignMode === 'email' && <div><Label htmlFor="messageTitle">Subjek Email</Label><Input id="messageTitle" value={messageTitle} onChange={e => setMessageTitle(e.target.value)} className="bg-gray-700 border-gray-600" /></div>}
        <div>
          <Label htmlFor="messageContent">Isi Pesan</Label>
          <Textarea id="messageContent" value={messageContent} onChange={e => setMessageContent(e.target.value)} rows={10} placeholder="Gunakan variabel: {nama}, {tanggal}, {paket}" className="bg-gray-700 border-gray-600 min-h-[200px]" />
          <p className="text-xs text-gray-500 mt-1">🚧 Rich Text Editor (QuillJS/TinyMCE) akan diimplementasikan. 🚧</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 items-end">
          <div className="grow"><Label htmlFor="loadTemplate">Muat Template</Label>
          <Select onValueChange={handleLoadTemplate} value={selectedTemplate}>
              <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue placeholder="Pilih template..." /></SelectTrigger>
              <SelectContent className="bg-gray-800 text-white border-gray-700">
                  {templates.map(t => <SelectItem key={t.name} value={t.name} className="hover:bg-primary/20">{t.name}</SelectItem>)}
                  {templates.length === 0 && <p className="p-2 text-sm text-gray-400">Belum ada template.</p>}
              </SelectContent>
          </Select>
          {selectedTemplate && <Button variant="ghost" size="sm" onClick={() => handleDeleteTemplate(selectedTemplate)} className="text-red-400 hover:text-red-500 mt-1"><Trash2 size={14} className="mr-1"/> Hapus Template Terpilih</Button>}
          </div>
          <Button onClick={() => setIsTemplateModalOpen(true)} variant="outline" className="text-primary border-primary hover:bg-primary/10">Simpan Sebagai Template Baru</Button>
        </div>
      </CardContent>
      <Dialog open={isTemplateModalOpen} onOpenChange={setIsTemplateModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white">
          <DialogHeader><DialogTitle className="text-primary">Simpan Pesan Sebagai Template</DialogTitle></DialogHeader>
          <div className="space-y-3 py-2">
            <div><Label htmlFor="newTemplateName">Nama Template</Label><Input id="newTemplateName" value={newTemplateName} onChange={e => setNewTemplateName(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <p className="text-sm text-gray-400">Isi pesan dan subjek (jika ada) saat ini akan disimpan.</p>
          </div>
          <DialogPrimitiveFooter>
            <Button variant="outline" onClick={() => setIsTemplateModalOpen(false)} className="text-gray-300 border-gray-600">Batal</Button>
            <Button onClick={handleSaveTemplate} className="gold-gradient text-black"><Save size={16} className="mr-2"/> Simpan Template</Button>
          </DialogPrimitiveFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default MessageEditorTab;
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Eye, MessageSquare, Mail, Trash2, CheckCircle, XCircle, Loader, Edit } from 'lucide-react';

const OrderActions = ({ order, onStatusChange, onDelete }) => {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const navigate = useNavigate();

    const handleViewDetails = () => {
        navigate(`/admin/orders/${order.id}`);
    };

    const handleWhatsApp = () => {
        const phone = order.formData.bookerInfo.whatsapp.replace(/\D/g, '');
        const message = `Halo ${order.formData.bookerInfo.picName}, kami dari Arrahmah Handling Service ingin mengkonfirmasi pesanan Anda dengan ID: ${order.id}.`;
        const url = `https://wa.me/${phone}?text=${encodeURIComponent(message)}`;
        window.open(url, '_blank');
    };

    const handleEmail = () => {
        const email = order.formData.bookerInfo.email;
        const subject = `Konfirmasi Pesanan Arrahmah Handling Service - ID: ${order.id}`;
        const body = `Halo ${order.formData.bookerInfo.picName},\n\nTerima kasih telah melakukan pemesanan di Arrahmah Handling Service. Ini adalah konfirmasi untuk pesanan Anda dengan ID: ${order.id}.\n\nKami akan segera memprosesnya.\n\nTerima kasih,\nTim Arrahmah Handling Service`;
        const url = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.location.href = url;
    };

    const handleDeleteClick = () => {
        setIsDeleteDialogOpen(true);
    };

    const confirmDelete = () => {
        onDelete(order.id);
        setIsDeleteDialogOpen(false);
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Buka menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700 text-white">
                    <DropdownMenuLabel>Aksi Cepat</DropdownMenuLabel>
                    <DropdownMenuItem onClick={handleViewDetails} className="cursor-pointer focus:bg-gray-700 focus:text-white">
                        <Eye className="mr-2 h-4 w-4" />
                        Lihat & Kelola Detail
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleWhatsApp} className="cursor-pointer focus:bg-gray-700 focus:text-white">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Hubungi via WhatsApp
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleEmail} className="cursor-pointer focus:bg-gray-700 focus:text-white">
                        <Mail className="mr-2 h-4 w-4" />
                        Kirim Email
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-gray-700" />
                    <DropdownMenuLabel>Ubah Status</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => onStatusChange(order.id, 'Diproses')} className="cursor-pointer focus:bg-gray-700 focus:text-white">
                        <Loader className="mr-2 h-4 w-4" />
                        Tandai "Diproses"
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onStatusChange(order.id, 'Selesai')} className="cursor-pointer focus:bg-gray-700 focus:text-white">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Tandai "Selesai"
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onStatusChange(order.id, 'Dibatalkan')} className="cursor-pointer focus:bg-gray-700 focus:text-white">
                        <XCircle className="mr-2 h-4 w-4" />
                        Tandai "Dibatalkan"
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-gray-700" />
                    <DropdownMenuItem onClick={handleDeleteClick} className="cursor-pointer text-red-400 focus:bg-red-900/50 focus:text-red-300">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Hapus Pesanan
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent className="bg-gray-800 border-gray-700 text-white">
                    <AlertDialogHeader>
                        <AlertDialogTitle>Anda yakin ingin menghapus pesanan ini?</AlertDialogTitle>
                        <AlertDialogDescription className="text-gray-400">
                            Tindakan ini tidak dapat diurungkan. Pesanan dengan ID <span className="font-bold text-amber-400">{order.id}</span> akan dihapus secara permanen.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel asChild>
                            <Button variant="outline" className="text-gray-300 border-gray-600 hover:bg-gray-700">Batal</Button>
                        </AlertDialogCancel>
                        <AlertDialogAction asChild>
                            <Button variant="destructive" onClick={confirmDelete}>Ya, Hapus</Button>
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default OrderActions;
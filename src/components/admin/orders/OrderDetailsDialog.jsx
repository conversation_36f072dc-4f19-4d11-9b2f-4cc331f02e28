import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import OrderStatusBadge from './OrderStatusBadge';

const DetailItem = ({ label, value, className = '' }) => (
    <div className={`grid grid-cols-3 gap-2 py-2 border-b border-gray-700 ${className}`}>
        <dt className="font-semibold text-gray-300 col-span-1">{label}</dt>
        <dd className="text-gray-200 col-span-2">{value || '-'}</dd>
    </div>
);

const OrderDetailsDialog = ({ order, isOpen, onClose }) => {
    if (!order) return null;

    const { bookerInfo, packageType, totalPax, summary, flightDetails, hotelDetails, roomComposition, optionalAddons, mutawwifRequest, visaStatus } = order.formData;

    const packageTypeLabels = {
        handling_service: 'Paket Handling Service',
        bundling_visa_handling: 'Paket Bundling Visa + Handling',
        la_b2b: 'Paket LA B2B',
        handling_airport_only: 'Paket Handling Airport Only',
        custom_request: 'Custom Request',
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-3xl bg-gray-800 border-gray-700 text-white">
                <DialogHeader>
                    <DialogTitle className="text-2xl text-white">Detail Order: {order.id}</DialogTitle>
                    <DialogDescription className="text-gray-400">
                        Dipesan oleh {bookerInfo.travelName} pada {format(new Date(order.createdAt), "d MMMM yyyy, HH:mm", { locale: id })}
                    </DialogDescription>
                </DialogHeader>
                <ScrollArea className="max-h-[70vh] pr-4">
                    <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <OrderStatusBadge status={order.status} />
                            <Badge variant="secondary" className="w-fit justify-self-end">{packageTypeLabels[packageType]}</Badge>
                        </div>

                        <section>
                            <h3 className="text-lg font-semibold text-amber-400 mb-2">Informasi Pemesan</h3>
                            <dl>
                                <DetailItem label="Nama Travel" value={bookerInfo.travelName} />
                                <DetailItem label="Nama PIC" value={bookerInfo.picName} />
                                <DetailItem label="WhatsApp" value={bookerInfo.whatsapp} />
                                <DetailItem label="Email" value={bookerInfo.email} />
                                <DetailItem label="Asal Jamaah" value={bookerInfo.jamaahOrigin} />
                            </dl>
                        </section>

                        <section>
                            <h3 className="text-lg font-semibold text-amber-400 mb-2">Ringkasan Paket</h3>
                            <dl>
                                <DetailItem label="Nama Paket" value={summary.packageName} />
                                <DetailItem label="Total Jamaah" value={`${totalPax} orang`} />
                                <DetailItem label="Harga per Jamaah" value={`$${summary.pricePerPax?.toFixed(2)}`} />
                                <DetailItem label="Subtotal" value={`$${summary.subtotal?.toFixed(2)}`} />
                            </dl>
                        </section>

                        {summary.addons && summary.addons.length > 0 && (
                            <section>
                                <h3 className="text-lg font-semibold text-amber-400 mb-2">Layanan Tambahan</h3>
                                <dl>
                                    {summary.addons.map((addon, index) => (
                                        <DetailItem key={index} label={addon.name} value={`$${addon.cost.toFixed(2)}`} />
                                    ))}
                                </dl>
                            </section>
                        )}

                        <section>
                            <h3 className="text-lg font-semibold text-amber-400 mb-2">Total Biaya</h3>
                            <dl>
                                <DetailItem label="Total Keseluruhan" value={`$${summary.total?.toFixed(2)}`} className="text-xl font-bold text-green-400" />
                            </dl>
                        </section>

                        {flightDetails && (
                            <section>
                                <h3 className="text-lg font-semibold text-amber-400 mb-2">Detail Penerbangan</h3>
                                <dl>
                                    <DetailItem label="Tgl Keberangkatan" value={flightDetails.departureDate ? format(new Date(flightDetails.departureDate), 'PPP', { locale: id }) : '-'} />
                                    <DetailItem label="Maskapai Keberangkatan" value={flightDetails.departureAirline} />
                                    <DetailItem label="No. Penerbangan" value={flightDetails.departureFlightNumber} />
                                    <DetailItem label="Tgl Kepulangan" value={flightDetails.returnDate ? format(new Date(flightDetails.returnDate), 'PPP', { locale: id }) : '-'} />
                                    <DetailItem label="Maskapai Kepulangan" value={flightDetails.returnAirline} />
                                    <DetailItem label="No. Penerbangan" value={flightDetails.returnFlightNumber} />
                                    <DetailItem label="Status Tiket" value={flightDetails.ticketStatus} />
                                </dl>
                            </section>
                        )}
                        
                        {roomComposition && Object.values(roomComposition).some(v => v > 0) && (
                             <section>
                                <h3 className="text-lg font-semibold text-amber-400 mb-2">Komposisi Kamar</h3>
                                <dl>
                                    <DetailItem label="Quad" value={roomComposition.quad} />
                                    <DetailItem label="Triple" value={roomComposition.triple} />
                                    <DetailItem label="Double" value={roomComposition.double} />
                                    <DetailItem label="Single" value={roomComposition.single} />
                                </dl>
                            </section>
                        )}

                    </div>
                </ScrollArea>
            </DialogContent>
        </Dialog>
    );
};

export default OrderDetailsDialog;
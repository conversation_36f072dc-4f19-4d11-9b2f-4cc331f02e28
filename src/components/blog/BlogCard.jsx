import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON>, CardFooter, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Badge } from '@/components/ui/badge.jsx';
import { ArrowRight, CalendarDays, UserCircle, Eye } from 'lucide-react';

const BlogCard = ({ 
  post, 
  formatDate, 
  getExcerpt, 
  blogSettings, 
  translations,
  placeholderImage 
}) => {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      variants={itemVariants}
      whileHover={{ y: -5 }}
      className="h-full"
    >
      <Card className="bg-linear-to-br from-gray-800 to-gray-900 border border-gray-700/50 text-white shadow-xl hover:shadow-glow transition-all duration-300 ease-in-out flex flex-col h-full overflow-hidden rounded-xl group">
        <CardHeader className="p-0">
          <Link to={`/blog/${post.slug}`} className="block aspect-video overflow-hidden relative">
            <img
              src={post.featured_image || placeholderImage}
              alt={post.title || "Blog post image"}
              className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-linear-to-t from-black/60 via-transparent to-transparent" />
            
            {post.category && blogSettings.showCategory && (
              <Badge 
                variant="secondary" 
                className="absolute top-4 left-4 bg-primary text-black font-semibold"
              >
                {post.category}
              </Badge>
            )}
            
            {post.views !== undefined && (
              <Badge 
                variant="outline" 
                className="absolute top-4 right-4 bg-black/50 text-white border-white/30 backdrop-blur-xs"
              >
                <Eye size={12} className="mr-1" />
                {post.views}
              </Badge>
            )}
          </Link>
          
          <div className="p-6">
            <CardTitle className="text-xl font-semibold text-primary mb-3 leading-tight min-h-[3.25em] line-clamp-2 group-hover:text-primary/80 transition-colors" title={post.title}>
              <Link to={`/blog/${post.slug}`}>{post.title}</Link>
            </CardTitle>
            
            <div className="flex items-center text-xs text-gray-400 space-x-3 mb-3">
              {blogSettings.showDate && (
                <span className="flex items-center">
                  <CalendarDays size={14} className="mr-1.5" /> 
                  {formatDate(post.publish_date)}
                </span>
              )}
              {blogSettings.showAuthor && (
                <span className="flex items-center">
                  <UserCircle size={14} className="mr-1.5" /> 
                  {post.author || "Admin"}
                </span>
              )}
            </div>
            
            {blogSettings.showExcerpt && (
              <CardDescription className="text-gray-300 line-clamp-3 text-sm leading-relaxed min-h-[4.5em]">
                {post.meta?.description || getExcerpt(post.content)}
              </CardDescription>
            )}
            
            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-4">
                {post.tags.slice(0, 3).map(tag => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="text-xs text-gray-400 border-gray-600 hover:border-primary hover:text-primary transition-colors cursor-pointer"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardFooter className="mt-auto p-6 border-t border-gray-700/50">
          <Link to={`/blog/${post.slug}`} className="w-full">
            <Button 
              variant="outline" 
              className="w-full text-primary border-primary hover:bg-primary hover:text-black transition-all duration-300 group-hover:shadow-md group-hover:shadow-primary/20"
            >
              {translations.readMore || "Baca Selengkapnya"} 
              <ArrowRight size={18} className="ml-2 transition-transform duration-300 group-hover:translate-x-1" />
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default BlogCard;
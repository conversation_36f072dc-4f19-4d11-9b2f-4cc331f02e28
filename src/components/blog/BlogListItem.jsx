import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Badge } from '@/components/ui/badge.jsx';
import { ArrowRight, CalendarDays, UserCircle } from 'lucide-react';

const BlogListItem = ({ 
  post, 
  formatDate, 
  getExcerpt, 
  blogSettings, 
  translations,
  placeholderImage 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      whileHover={{ y: -2 }}
    >
      <Card className="bg-linear-to-br from-gray-800 to-gray-900 border border-gray-700/50 text-white shadow-xl hover:shadow-glow transition-all duration-300 ease-in-out flex flex-col md:flex-row overflow-hidden rounded-xl group">
        <div className="md:w-1/3 lg:w-2/5 xl:w-1/3 shrink-0">
          <Link to={`/blog/${post.slug}`} className="block h-full aspect-video md:aspect-auto">
            <img
              src={post.featured_image || placeholderImage}
              alt={post.title || "Blog post image"}
              className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
            />
          </Link>
        </div>
        <CardContent className="p-6 flex flex-col flex-1">
          {post.category && blogSettings.showCategory && (
            <Badge 
              variant="secondary" 
              className="bg-primary text-black font-semibold mb-3 self-start"
            >
              {post.category}
            </Badge>
          )}
          
          <h3 className="text-xl font-semibold text-primary mb-3 leading-tight line-clamp-2 group-hover:text-primary/80 transition-colors" title={post.title}>
             <Link to={`/blog/${post.slug}`}>{post.title}</Link>
          </h3>
          
          <div className="flex items-center text-xs text-gray-400 space-x-4 mb-4">
            {blogSettings.showDate && (
              <span className="flex items-center">
                <CalendarDays size={14} className="mr-1.5" /> 
                {formatDate(post.publish_date)}
              </span>
            )}
            {blogSettings.showAuthor && (
              <span className="flex items-center">
                <UserCircle size={14} className="mr-1.5" /> 
                {post.author || "Admin"}
              </span>
            )}
          </div>
          
          {blogSettings.showExcerpt && (
            <p className="text-gray-300 line-clamp-3 text-sm leading-relaxed mb-4">
              {post.meta?.description || getExcerpt(post.content, 150)}
            </p>
          )}

          <div className="mt-auto flex justify-between items-center">
            <Link to={`/blog/${post.slug}`}>
              <Button 
                variant="outline" 
                className="text-primary border-primary hover:bg-primary hover:text-black transition-all duration-300 group-hover:shadow-md group-hover:shadow-primary/20"
              >
                {translations.readMore || "Baca Selengkapnya"} 
                <ArrowRight size={16} className="ml-2 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </Link>
            {post.tags && post.tags.length > 0 && (
              <div className="hidden sm:flex flex-wrap gap-1 justify-end">
                {post.tags.slice(0, 2).map(tag => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="text-xs text-gray-400 border-gray-600"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default BlogListItem;
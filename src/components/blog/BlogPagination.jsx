import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

const BlogPagination = ({ 
  currentPage, 
  totalPages, 
  onPageChange, 
  translations 
}) => {
  if (totalPages <= 1) return null;

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const visiblePages = getVisiblePages();

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex justify-center items-center space-x-2 mt-12"
    >
      {/* Previous Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="text-primary border-primary hover:bg-primary hover:text-black disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ChevronLeft size={16} className="mr-1" />
        {translations.previous || "Sebelumnya"}
      </Button>
      
      {/* Page Numbers */}
      <div className="flex space-x-1">
        {visiblePages.map((page, index) => (
          <React.Fragment key={index}>
            {page === '...' ? (
              <Button
                variant="ghost"
                size="sm"
                disabled
                className="text-gray-400"
              >
                <MoreHorizontal size={16} />
              </Button>
            ) : (
              <Button
                variant={currentPage === page ? "default" : "outline-solid"}
                size="sm"
                onClick={() => onPageChange(page)}
                className={currentPage === page 
                  ? "bg-primary text-black hover:bg-primary/90" 
                  : "text-primary border-primary hover:bg-primary hover:text-black"
                }
              >
                {page}
              </Button>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Next Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="text-primary border-primary hover:bg-primary hover:text-black disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {translations.next || "Selanjutnya"}
        <ChevronRight size={16} className="ml-1" />
      </Button>
    </motion.div>
  );
};

export default BlogPagination;
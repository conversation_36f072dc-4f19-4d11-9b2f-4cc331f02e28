import React from 'react';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { User, Mail, Phone, BookText } from 'lucide-react';

const ContactFormInputs = ({ formData, handleInputChange }) => {
  const inputFields = [
    { id: 'name', label: '<PERSON><PERSON>', type: 'text', placeholder: 'Masukkan nama lengkap Anda', icon: <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" /> },
    { id: 'email', label: '<PERSON>amat Email', type: 'email', placeholder: '<EMAIL>', icon: <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" /> },
    { id: 'phone', label: '<PERSON><PERSON> Telepon (WhatsApp)', type: 'tel', placeholder: '+62 812 3456 7890', icon: <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" /> },
    { id: 'subject', label: 'Subjek Pesan', type: 'text', placeholder: 'Contoh: Pertanyaan Paket VIP', icon: <BookText className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" /> },
  ];

  return (
    <>
      {inputFields.map(field => (
        <div key={field.id}>
          <Label htmlFor={field.id} className="text-gray-300 mb-2 block font-medium">
            {field.label}
          </Label>
          <div className="relative">
            {field.icon}
            <Input 
              type={field.type} 
              name={field.id} 
              id={field.id} 
              value={formData[field.id] || ''} 
              onChange={handleInputChange} 
              required
              className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-500 focus:border-amber-400 focus:ring-amber-400 pl-10 py-3 h-12 text-base" 
              placeholder={field.placeholder} 
            />
          </div>
        </div>
      ))}

      <div>
        <Label htmlFor="message" className="text-gray-300 mb-2 block font-medium">
          Pesan Anda
        </Label>
        <Textarea 
          name="message" 
          id="message" 
          rows={5} 
          value={formData.message || ''} 
          onChange={handleInputChange} 
          required
          className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-500 focus:border-amber-400 focus:ring-amber-400 resize-none text-base p-3" 
          placeholder="Tuliskan detail pertanyaan atau kebutuhan Anda di sini..."
        />
      </div>
    </>
  );
};

export default ContactFormInputs;
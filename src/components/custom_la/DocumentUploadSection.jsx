import React, { useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload, FileText, X } from 'lucide-react';

const FileUploadItem = ({ id, label, fileName, onFileChange, onRemoveFile }) => {
    const inputRef = useRef(null);

    return (
        <div className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
            <Label htmlFor={id} className="cursor-pointer">{label}</Label>
            <div className="flex items-center gap-2">
                {fileName ? (
                    <>
                        <span className="text-sm text-green-400 flex items-center"><FileText className="h-4 w-4 mr-1.5" /> {fileName}</span>
                        <Button variant="ghost" size="icon" className="h-7 w-7" onClick={onRemoveFile}>
                            <X className="h-4 w-4" />
                        </Button>
                    </>
                ) : (
                    <Button variant="outline" size="sm" onClick={() => inputRef.current.click()}>
                        <Upload className="mr-2 h-4 w-4" />
                        Upload
                    </Button>
                )}
                <input
                    type="file"
                    id={id}
                    ref={inputRef}
                    className="hidden"
                    onChange={onFileChange}
                />
            </div>
        </div>
    );
};

const DocumentUploadSection = ({ formData, handleFileChange, removeFile }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">I</span>
                    Upload Dokumen (Opsional)
                </CardTitle>
                <CardDescription>Unggah dokumen untuk mempercepat proses verifikasi.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
                <FileUploadItem
                    id="roomlist-upload"
                    label="Roomlist Jamaah (Excel/PDF)"
                    fileName={formData.roomlist?.name}
                    onFileChange={(e) => handleFileChange('roomlist', e.target.files[0])}
                    onRemoveFile={() => removeFile('roomlist')}
                />
                <FileUploadItem
                    id="ticket-upload"
                    label="Tiket Pesawat"
                    fileName={formData.ticket?.name}
                    onFileChange={(e) => handleFileChange('ticket', e.target.files[0])}
                    onRemoveFile={() => removeFile('ticket')}
                />
                <FileUploadItem
                    id="visa-upload"
                    label="Visa Umrah"
                    fileName={formData.visa?.name}
                    onFileChange={(e) => handleFileChange('visa', e.target.files[0])}
                    onRemoveFile={() => removeFile('visa')}
                />
                <FileUploadItem
                    id="manifest-upload"
                    label="Manifest (Excel/PDF)"
                    fileName={formData.manifest?.name}
                    onFileChange={(e) => handleFileChange('manifest', e.target.files[0])}
                    onRemoveFile={() => removeFile('manifest')}
                />
            </CardContent>
        </Card>
    );
};

export default DocumentUploadSection;
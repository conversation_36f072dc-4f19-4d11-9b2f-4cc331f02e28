import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CheckCircle, Award, Star } from 'lucide-react';

const HandlingOption = ({ id, value, label, description, checked, icon }) => (
    <Label
        htmlFor={id}
        className={`flex items-start p-4 border rounded-lg cursor-pointer transition-all duration-300 ${
            checked ? 'border-amber-400 bg-amber-500/10' : 'border-gray-700 bg-gray-900/50 hover:border-amber-400/50'
        }`}
    >
        <RadioGroupItem value={value} id={id} className="mt-1" />
        <div className="grow ml-4">
            <span className="text-lg font-bold text-white flex items-center">
                {icon}
                {label}
            </span>
            <p className="text-gray-400 mt-1">{description}</p>
        </div>
    </Label>
);

const HandlingPackageSection = ({ formData, handleInputChange }) => {
    const handlingDetails = {
        Basic: "Welcome drink (Zamzam + Kurma), handling hotel & airport, koper, snack 4x, dokumentasi, tips porter & driver",
        Premium: "Semua fasilitas Basic + X-Banner, Al Baik 1x, snack premium, parcel buah, air mineral",
        VIP: "Semua fasilitas Basic + Arabian kuliner Al Romanshiah, snack VIP, parcel VIP, Zamzam galon, Al Baik (Madinah-Makkah)"
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">E</span>
                    Pilih Paket Handling
                </CardTitle>
                <CardDescription>Pilih jenis pelayanan handling yang sesuai dengan kebutuhan jamaah Anda.</CardDescription>
            </CardHeader>
            <CardContent>
                <RadioGroup
                    value={formData}
                    onValueChange={(value) => handleInputChange('type', value)}
                    className="space-y-4"
                >
                    <HandlingOption
                        id="handling-basic"
                        value="Basic"
                        label="Basic"
                        description={handlingDetails.Basic}
                        checked={formData === 'Basic'}
                        icon={<CheckCircle className="mr-2 h-5 w-5 text-gray-400" />}
                    />
                    <HandlingOption
                        id="handling-premium"
                        value="Premium"
                        label="Premium"
                        description={handlingDetails.Premium}
                        checked={formData === 'Premium'}
                        icon={<Award className="mr-2 h-5 w-5 text-amber-400" />}
                    />
                    <HandlingOption
                        id="handling-vip"
                        value="VIP"
                        label="VIP"
                        description={handlingDetails.VIP}
                        checked={formData === 'VIP'}
                        icon={<Star className="mr-2 h-5 w-5 text-yellow-300" />}
                    />
                </RadioGroup>
            </CardContent>
        </Card>
    );
};

export default HandlingPackageSection;
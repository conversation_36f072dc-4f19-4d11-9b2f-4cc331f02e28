import React, { useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { format, addDays } from "date-fns";
import { id } from "date-fns/locale";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon, Hotel as HotelIcon, Trash2, PlusCircle, Moon } from 'lucide-react';

const HotelCard = ({ hotel, index, onHotelChange, onRemove, previousHotelCheckout }) => {
    
    useEffect(() => {
        if (previousHotelCheckout && hotel.checkIn !== previousHotelCheckout) {
            onHotelChange(hotel.id, 'checkIn', previousHotelCheckout);
        }
    }, [previousHotelCheckout, hotel.checkIn, hotel.id, onHotelChange]);

    const handleNightsChange = (e) => {
        const nights = parseInt(e.target.value, 10);
        onHotelChange(hotel.id, 'nights', isNaN(nights) ? '' : nights);
    };
    
    const isCheckInDisabled = index > 0;

    return (
        <Card className="bg-gray-900/50 border-gray-700">
            <CardHeader className="flex flex-row items-center justify-between pb-4">
                <CardTitle className="text-lg text-amber-400 flex items-center">
                    <HotelIcon className="mr-3 h-5 w-5" />
                    Hotel #{index + 1}
                </CardTitle>
                <Button type="button" variant="ghost" size="icon" onClick={() => onRemove(hotel.id)}>
                    <Trash2 className="h-4 w-4 text-red-500 hover:text-red-400" />
                </Button>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label>Kota</Label>
                        <Select value={hotel.city} onValueChange={(value) => onHotelChange(hotel.id, 'city', value)}>
                            <SelectTrigger><SelectValue placeholder="Pilih kota" /></SelectTrigger>
                            <SelectContent>
                                <SelectItem value="Makkah">Makkah</SelectItem>
                                <SelectItem value="Madinah">Madinah</SelectItem>
                                <SelectItem value="Jeddah">Jeddah</SelectItem>
                                <SelectItem value="Taif">Taif</SelectItem>
                                <SelectItem value="Abha">Abha</SelectItem>
                                <SelectItem value="Lainnya">Lainnya</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label>Nama Hotel</Label>
                        <Input placeholder="Contoh: Movenpick Hajar Tower" value={hotel.hotelName} onChange={(e) => onHotelChange(hotel.id, 'hotelName', e.target.value)} />
                    </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-end">
                    <div className="space-y-2">
                        <Label>Tanggal Check-in</Label>
                         <Popover>
                            <PopoverTrigger asChild>
                                <Button 
                                    type="button"
                                    variant="outline" 
                                    className="w-full justify-start text-left font-normal"
                                    disabled={isCheckInDisabled}
                                >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {hotel.checkIn ? format(new Date(hotel.checkIn), "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                                </Button>
                            </PopoverTrigger>
                            {!isCheckInDisabled && (
                                <PopoverContent className="w-auto p-0">
                                    <Calendar 
                                        mode="single" 
                                        selected={hotel.checkIn ? new Date(hotel.checkIn) : null} 
                                        onSelect={(date) => onHotelChange(hotel.id, 'checkIn', date)} 
                                        disabled={{ before: new Date() }}
                                    />
                                </PopoverContent>
                            )}
                        </Popover>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor={`nights-${hotel.id}`}>Jumlah Malam</Label>
                        <Input id={`nights-${hotel.id}`} type="number" min="1" placeholder="Malam" value={hotel.nights} onChange={handleNightsChange} />
                    </div>
                    <div className="space-y-2">
                        <Label>Tanggal Check-out</Label>
                        <div className="flex h-10 w-full items-center rounded-md border border-input bg-background/50 px-3 py-2 text-sm">
                            {hotel.checkOut ? format(new Date(hotel.checkOut), "PPP", { locale: id }) : <span className="text-muted-foreground">Otomatis</span>}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

const HotelInputSection = ({ formData, handleHotelChange, setFormData }) => {
    const addHotel = () => {
        const lastHotel = formData[formData.length - 1];
        const newCheckIn = lastHotel ? lastHotel.checkOut : null;

        setFormData(prev => ({
            ...prev,
            hotels: [...prev.hotels, { id: uuidv4(), city: '', hotelName: '', checkIn: newCheckIn, nights: '', checkOut: null }]
        }));
    };

    const removeHotel = (id) => {
        setFormData(prev => ({
            ...prev,
            hotels: prev.hotels.filter(h => h.id !== id)
        }));
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">D</span>
                    Input Hotel Dinamis
                </CardTitle>
                <CardDescription>Tambahkan hotel sesuai rencana perjalanan Anda. Urutan check-in akan diatur otomatis.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                {formData.map((hotel, index) => (
                    <HotelCard
                        key={hotel.id}
                        hotel={hotel}
                        index={index}
                        onHotelChange={handleHotelChange}
                        onRemove={removeHotel}
                        previousHotelCheckout={index > 0 ? formData[index - 1]?.checkOut : null}
                    />
                ))}
                <Button type="button" variant="outline" onClick={addHotel} className="w-full">
                    <PlusCircle className="mr-2 h-4 w-4" /> Tambah Hotel Lagi
                </Button>
            </CardContent>
        </Card>
    );
};

export default HotelInputSection;
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Users, BedSingle, BedDouble, Bed } from 'lucide-react';

const RoomField = ({ id, label, value, onChange, icon }) => (
    <div className="space-y-2">
        <Label htmlFor={id} className="flex items-center">{icon}{label}</Label>
        <Input
            id={id}
            type="number"
            min="0"
            placeholder="0"
            value={value}
            onChange={onChange}
            className="text-center"
        />
    </div>
);

const RoomCompositionSection = ({ formData, handleInputChange, totalJamaah }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">C</span>
                    Komposisi Kamar
                </CardTitle>
                <CardDescription>Masukkan jumlah kamar yang dibutuhkan per tipe. Total jamaah akan dihitung otomatis.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <RoomField
                        id="quadRooms"
                        label="Quad (4)"
                        value={formData.quad}
                        onChange={(e) => handleInputChange('quad', e.target.value)}
                        icon={<Bed className="mr-2 h-4 w-4" />}
                    />
                    <RoomField
                        id="tripleRooms"
                        label="Triple (3)"
                        value={formData.triple}
                        onChange={(e) => handleInputChange('triple', e.target.value)}
                        icon={<Bed className="mr-2 h-4 w-4" />}
                    />
                    <RoomField
                        id="doubleRooms"
                        label="Double (2)"
                        value={formData.double}
                        onChange={(e) => handleInputChange('double', e.target.value)}
                        icon={<BedDouble className="mr-2 h-4 w-4" />}
                    />
                    <RoomField
                        id="singleRooms"
                        label="Single (1)"
                        value={formData.single}
                        onChange={(e) => handleInputChange('single', e.target.value)}
                        icon={<BedSingle className="mr-2 h-4 w-4" />}
                    />
                </div>
                <div className="flex items-center justify-center p-4 bg-gray-900/50 rounded-lg">
                    <Users className="h-8 w-8 text-amber-400 mr-4" />
                    <div>
                        <p className="text-sm text-gray-400">Total Jamaah</p>
                        <p className="text-3xl font-bold text-white">{totalJamaah}</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default RoomCompositionSection;
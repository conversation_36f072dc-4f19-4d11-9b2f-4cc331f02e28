import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Users, Moon, Hotel, MapPin, Package, Check, Plane } from 'lucide-react';
import { format } from "date-fns";
import { id } from "date-fns/locale";

const SummaryItem = ({ icon, label, value, children }) => (
    <div>
        <div className="flex items-center text-sm text-gray-400">
            {icon}
            {label}
        </div>
        {value && <p className="font-semibold text-white pl-6">{value}</p>}
        {children && <div className="pl-6 text-sm text-gray-200">{children}</div>}
    </div>
);

const SummarySidebar = ({ formData, totalJamaah, totalNights }) => {
    const { flightInfo, hotels, handlingPackage, additionalPrograms, mutawwifRequest } = formData;

    const hasHaramainTrain = additionalPrograms?.haramainTrain?.routes && Object.values(additionalPrograms.haramainTrain.routes).some(v => v);
    const hasThaifTour = additionalPrograms?.thaifTour?.enabled;

    return (
        <Card className="bg-gray-900/80 border-gray-700 sticky top-28">
            <CardHeader>
                <CardTitle>Ringkasan Permintaan Custom</CardTitle>
                <CardDescription>Rekapitulasi otomatis dari form yang Anda isi. Harga akan diinformasikan kemudian.</CardDescription>
            </CardHeader>
            <CardContent>
                <ScrollArea className="h-[70vh] pr-4">
                    <div className="space-y-5">
                        <SummaryItem
                            icon={<Users className="mr-2 h-4 w-4" />}
                            label="Total Jamaah"
                            value={`${totalJamaah} orang`}
                        />
                        <SummaryItem
                            icon={<Moon className="mr-2 h-4 w-4" />}
                            label="Total Malam"
                            value={`${totalNights} malam`}
                        />
                        {flightInfo?.departureDate && (
                            <SummaryItem
                                icon={<Plane className="mr-2 h-4 w-4" />}
                                label="Keberangkatan"
                                value={format(new Date(flightInfo.departureDate), "d MMMM yyyy", { locale: id })}
                            />
                        )}
                        <SummaryItem icon={<Hotel className="mr-2 h-4 w-4" />} label="Itinerary Hotel & Kota">
                            {hotels && hotels.length > 0 ? (
                                <ul className="mt-1 space-y-1 list-disc list-inside">
                                    {hotels.map(hotel => (
                                        <li key={hotel.id}>
                                            <span className="font-semibold">{hotel.hotelName || "Nama hotel"}</span> di <span className="text-amber-300">{hotel.city || "Kota"}</span> ({hotel.nights || '0'} malam)
                                        </li>
                                    ))}
                                </ul>
                            ) : <p className="text-gray-500">Belum ada hotel ditambahkan.</p>}
                        </SummaryItem>
                         <SummaryItem icon={<Package className="mr-2 h-4 w-4" />} label="Paket Handling" value={handlingPackage?.type}/>

                        {(hasThaifTour || hasHaramainTrain) && (
                            <SummaryItem icon={<MapPin className="mr-2 h-4 w-4" />} label="Program Tambahan">
                                <ul className="mt-1 space-y-1 list-disc list-inside">
                                     {hasThaifTour && <li>Tour Thaif</li>}
                                     {hasHaramainTrain && <li>Kereta Cepat Haramain</li>}
                                </ul>
                            </SummaryItem>
                        )}
                         
                        {mutawwifRequest?.needed === 'Ya' && (
                           <SummaryItem icon={<Check className="mr-2 h-4 w-4" />} label="Permintaan Mutawwif" value="Ya, dengan kriteria khusus."/>
                        )}
                    </div>
                </ScrollArea>
            </CardContent>
        </Card>
    );
};

export default SummarySidebar;
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User, Users, Mail, Phone, MapPin } from 'lucide-react';

const FormField = ({ id, label, placeholder, value, onChange, icon, isOptional = false, type = 'text' }) => (
    <div className="space-y-2">
        <Label htmlFor={id} className="flex items-center text-gray-300">
            {icon}
            {label} {!isOptional && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <Input
            id={id}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            required={!isOptional}
            type={type}
        />
    </div>
);

const TravelInfoSection = ({ formData, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                 <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">1</span>
                    Informasi Travel / Pemesan
                </CardTitle>
                <CardDescription>Lengkapi detail dasar mengenai pemesan dan travel agent.</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                    id="travelName"
                    label="Nama Travel / Agen Umrah"
                    placeholder="Contoh: Barokah Travel"
                    value={formData.travelName}
                    onChange={(e) => handleInputChange('travelName', e.target.value)}
                    icon={<Users className="mr-2 h-4 w-4" />}
                />
                <FormField
                    id="picName"
                    label="Nama PIC (Penanggung Jawab)"
                    placeholder="Contoh: Ahmad"
                    value={formData.picName}
                    onChange={(e) => handleInputChange('picName', e.target.value)}
                    icon={<User className="mr-2 h-4 w-4" />}
                />
                <FormField
                    id="whatsapp"
                    label="Nomor WhatsApp Aktif"
                    placeholder="+6281234567890"
                    value={formData.whatsapp}
                    onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                    icon={<Phone className="mr-2 h-4 w-4" />}
                />
                <FormField
                    id="email"
                    label="Email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    icon={<Mail className="mr-2 h-4 w-4" />}
                    isOptional={true}
                    type="email"
                />
                <FormField
                    id="jamaahOrigin"
                    label="Asal Jamaah"
                    placeholder="Contoh: Jakarta, Indonesia"
                    value={formData.jamaahOrigin}
                    onChange={(e) => handleInputChange('jamaahOrigin', e.target.value)}
                    icon={<MapPin className="mr-2 h-4 w-4" />}
                />
            </CardContent>
        </Card>
    );
};

export default TravelInfoSection;
import React from 'react';
import { motion } from 'framer-motion';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion.jsx";
import { Plus, Minus } from 'lucide-react';
import { getLocalizedContent } from './FaqTranslations.js';

const FaqAccordion = ({ items, language }) => {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: "easeOut"
      },
    }),
  };

  return (
    <Accordion type="single" collapsible className="w-full space-y-4">
      {items.map((item, index) => (
        <motion.div
          key={item.id}
          custom={index}
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          <AccordionItem
            value={`item-${index}`}
            className="bg-linear-to-br from-gray-800/60 to-gray-900/50 rounded-xl border border-gray-700/80 backdrop-blur-xs transition-all duration-300 hover:border-amber-500/50 data-[state=open]:border-amber-500/80 shadow-lg"
          >
            <AccordionTrigger className="text-left text-lg font-semibold text-white hover:text-amber-400 p-6 focus:outline-hidden focus-visible:ring-2 focus-visible:ring-amber-500 rounded-lg group">
              <span className="flex-1 pr-6">
                {getLocalizedContent(item, 'question', language)}
              </span>
              <div className="relative h-7 w-7 shrink-0 flex items-center justify-center text-gray-400 group-hover:text-amber-400 transition-colors duration-300">
                <Plus className="absolute h-7 w-7 transition-all duration-300 transform scale-100 group-data-[state=open]:scale-0 group-data-[state=open]:-rotate-90" />
                <Minus className="absolute h-7 w-7 transition-all duration-300 transform scale-0 group-data-[state=open]:scale-100 group-data-[state=open]:rotate-0" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="pt-0 p-6 text-gray-300 text-base leading-relaxed">
              <div
                className="prose prose-invert max-w-none prose-p:text-gray-300 prose-a:text-amber-400 prose-strong:text-amber-400"
                dangerouslySetInnerHTML={{
                  __html: getLocalizedContent(item, 'answer', language).replace(/\n/g, '<br/>')
                }}
              />
            </AccordionContent>
          </AccordionItem>
        </motion.div>
      ))}
    </Accordion>
  );
};

export default FaqAccordion;
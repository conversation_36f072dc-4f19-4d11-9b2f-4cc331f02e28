import React from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import FaqAccordion from './FaqCategory.jsx';
import { getCategoryTitle } from './FaqTranslations.js';

const FaqContent = ({ category, language }) => {
  return (
    <div className="w-full lg:w-3/4 lg:pl-10">
      <AnimatePresence mode="wait">
        <motion.div
          key={category.category}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <h2 className="text-3xl font-bold text-white mb-8 border-l-4 border-amber-400 pl-4">
            {getCategoryTitle(category.category, language)}
          </h2>
          <FaqAccordion items={category.items} language={language} />
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default FaqContent;
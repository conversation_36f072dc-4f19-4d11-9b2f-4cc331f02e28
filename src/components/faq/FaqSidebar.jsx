import React from 'react';
import { motion } from 'framer-motion';
import { getCategoryTitle } from './FaqTranslations.js';
import { getCategoryIcon } from './FaqCategoryIcons.jsx';

const FaqSidebar = ({ categories, activeCategory, language }) => {
  const handleCategoryClick = (e, category) => {
    e.preventDefault();
    const element = document.getElementById(category);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <aside className="hidden lg:block w-1/4 pr-8">
      <div className="sticky top-28">
        <p className="text-lg font-semibold text-white mb-4 px-4 capitalize">
          {language === 'ar' ? 'الفئات' : language === 'en' ? 'Categories' : 'Kategori'}
        </p>
        <motion.nav
          initial="hidden"
          animate="visible"
          variants={{
            visible: {
              transition: {
                staggerChildren: 0.05,
              },
            },
          }}
          className="space-y-1"
        >
          {categories.map((cat) => (
            <motion.a
              key={cat.category}
              href={`#${cat.category}`}
              onClick={(e) => handleCategoryClick(e, cat.category)}
              variants={{
                hidden: { opacity: 0, x: -20 },
                visible: { opacity: 1, x: 0 },
              }}
              className="faq-sidebar-link"
              data-active={activeCategory === cat.category}
            >
              <span className="faq-sidebar-icon mr-3 text-gray-400 transition-colors duration-200">
                {React.cloneElement(getCategoryIcon(cat.category), { className: "h-5 w-5" })}
              </span>
              <span>{getCategoryTitle(cat.category, language)}</span>
            </motion.a>
          ))}
        </motion.nav>
      </div>
    </aside>
  );
};

export default FaqSidebar;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { MessageCircle } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const CallToAction = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  return (
    <section className="py-20 bg-linear-to-r from-gray-900 to-black text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-pattern opacity-10"></div>
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true }}
            className="text-center lg:text-left"
          >
            <h2 className="text-4xl md:text-5xl font-extrabold mb-4 leading-tight gradient-text">
              {getTranslation('ctaContactTitle', 'Tingkatkan Kualitas Layanan Handling Umrah Anda')}
            </h2>
            <p className="text-lg md:text-xl text-gray-300 mb-8 max-w-xl lg:mx-0 mx-auto">
              {getTranslation('ctaContactSubtitle', 'Diskusikan kebutuhan land arrangement umroh Anda dengan tim ahli kami. Dapatkan penawaran terbaik hari ini.')}
            </p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <Button 
                onClick={() => window.open(`https://wa.me/6281280908093?text=${encodeURIComponent(getTranslation('whatsappGreeting', 'Assalamualaikum, saya tertarik dengan layanan Umrah Service.'))}`, '_blank')}
                className="gold-gradient text-black font-semibold text-lg px-8 py-6 rounded-lg shadow-lg hover:shadow-amber-500/60 transform hover:scale-105 transition-all duration-300 flex items-center justify-center mx-auto lg:mx-0"
              >
                {getTranslation('ctaButtonText', 'Hubungi via WhatsApp')}
                <MessageCircle className="ml-3 h-6 w-6" />
              </Button>
            </motion.div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true }}
            className="hidden lg:flex justify-center items-center"
          >
            <img  
              alt={getTranslation('ctaImageAlt', 'Layanan Umrah Profesional')} 
              className="w-full max-w-md h-auto rounded-xl shadow-2xl border border-amber-400/30"
             src="https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/ab07d2a8a1b188cd2083930791756492.jpg"
             loading="lazy" />
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;
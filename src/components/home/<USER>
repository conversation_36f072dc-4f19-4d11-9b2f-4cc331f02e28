import React, { useContext, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { Link } from 'react-router-dom';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { ChevronDown } from 'lucide-react';

const Hero = () => {
  const { translations } = useContext(LanguageContext);
  const [content, setContent] = useState({
    title: "Jasa Handling Umrah & <gold>Land Arrangement Umroh</gold> Profesional",
    subtitle: "Solusi #1 untuk travel agent. Percayakan layanan handling umroh dan land arrangement Anda kepada kami untuk kenyamanan jamaah di Makkah & Madina<PERSON>.",
    image: "https://images.unsplash.com/photo-1618672445860-e98bc47fb30e",
    buttonServicesText: "<PERSON><PERSON><PERSON>",
    buttonContactText: "<PERSON>bung<PERSON> Ka<PERSON>"
  });

  useEffect(() => {
    const storedContent = localStorage.getItem('homePageContent');
    if (storedContent) {
      const parsedContent = JSON.parse(storedContent);
      if (parsedContent.hero) {
        setContent(prevContent => ({
          ...prevContent,
          title: parsedContent.hero.title || prevContent.title,
          subtitle: parsedContent.hero.subtitle || prevContent.subtitle,
          image: parsedContent.hero.image || prevContent.image,
          buttonServicesText: parsedContent.hero.buttonServicesText || prevContent.buttonServicesText,
          buttonContactText: parsedContent.hero.buttonContactText || prevContent.buttonContactText,
        }));
      }
    }
  }, []);
  
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const heroTitle = getTranslation('heroTitle', content.title);
  const heroSubtitle = getTranslation('heroSubtitle', content.subtitle);
  const heroImage = content.image;
  const buttonServicesText = getTranslation('heroButtonServices', content.buttonServicesText);
  const buttonContactText = getTranslation('heroButtonContact', content.buttonContactText);


  const parseGoldText = (text) => {
    if (!text) return '';
    const parts = text.split(/<gold>|<\/gold>/g);
    return parts.map((part, index) => {
      if (index % 2 === 1) { // Text within <gold> tags
        return <span key={index} className="bg-clip-text text-transparent bg-linear-to-r from-amber-300 via-amber-400 to-yellow-500">{part}</span>;
      }
      return part; // Regular text
    });
  };


  return (
    <section className="relative min-h-screen flex items-center justify-center text-center text-white overflow-hidden bg-black">
      <div className="absolute inset-0 z-0">
        <motion.div 
          className="absolute inset-0"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        >
          <img  
            alt={getTranslation('heroImageAlt', 'Pemandangan Ka\'bah yang menenangkan untuk layanan handling umrah')}
            className="w-full h-full object-cover opacity-50"
            src={heroImage}
            loading="eager"
            fetchpriority="high"
          />
        </motion.div>
        <div className="absolute inset-0 bg-linear-to-t from-black via-black/70 to-transparent"></div>
      </div>
      
      <div className="relative z-10 container mx-auto px-4 md:px-6">
        <motion.h1 
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100, delay: 0.2 }}
          className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold mb-6 leading-tight tracking-tight"
        >
          {parseGoldText(heroTitle)}
        </motion.h1>
        
        <motion.p 
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5, type: "spring", stiffness: 100 }}
          className="text-lg md:text-xl lg:text-2xl mb-10 max-w-3xl mx-auto text-gray-300 leading-relaxed"
        >
          {heroSubtitle}
        </motion.p>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6"
        >
          <Button 
            asChild 
            className="gold-gradient text-black font-semibold text-lg px-10 py-7 rounded-lg shadow-lg hover:shadow-amber-500/60 transform hover:scale-105 transition-all duration-300"
          >
            <Link to="/services">{buttonServicesText}</Link>
          </Button>
          <Button 
            asChild 
            variant="outline" 
            className="border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-black text-lg px-10 py-7 rounded-lg shadow-md hover:shadow-amber-400/40 transform hover:scale-105 transition-all duration-300"
          >
            <Link to="/contact">{buttonContactText}</Link>
          </Button>
        </motion.div>
      </div>

      <motion.div
        className="absolute bottom-8 left-1/2 -translate-x-1/2"
        animate={{ y: [0, -10, 0] }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 1.2 }}
      >
        <ChevronDown className="text-white opacity-70 h-8 w-8" />
      </motion.div>
    </section>
  );
};

export default Hero;
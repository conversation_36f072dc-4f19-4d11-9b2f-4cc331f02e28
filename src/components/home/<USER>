import React, { useContext, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Shield<PERSON>heck, Users, Cpu, Globe2 } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const defaultFeatures = [
  {
    id: "legal",
    icon: <ShieldCheck className="w-10 h-10 mb-4 text-[#FFD700]" />,
    titleKey: "featureLegalTitle",
    descriptionKey: "featureLegalDesc",
    defaultTitle: "Legalitas Resmi & Terjamin",
    defaultDescription: "Layanan handling umrah kami didukung <PERSON> resmi <PERSON>, menjamin semua proses sesuai regulasi dan aman untuk jamaah Anda."
  },
  {
    id: "team",
    icon: <Users className="w-10 h-10 mb-4 text-[#FFD700]" />,
    titleKey: "featureExperienceTitle",
    descriptionKey: "featureExperienceDesc",
    defaultTitle: "Tim Handling Profesional",
    defaultDescription: "Tim lapangan kami berpengalaman puluhan tahun dalam jasa handling umroh, siap melayani jamaah dengan sigap, ramah, dan sepenuh hati."
  },
  {
    id: "tech",
    icon: <Cpu className="w-10 h-10 mb-4 text-[#FFD700]" />,
    titleKey: "featureTechTitle",
    descriptionKey: "featureTechDesc",
    defaultTitle: "Koordinasi Berbasis Teknologi",
    defaultDescription: "Gunakan aplikasi khusus untuk memonitor pergerakan jamaah dan koordinasi land arrangement secara real-time, memberikan ketenangan bagi travel agent."
  },
  {
    id: "network",
    icon: <Globe2 className="w-10 h-10 mb-4 text-[#FFD700]" />,
    titleKey: "featureNetworkTitle",
    descriptionKey: "featureNetworkDesc",
    defaultTitle: "Jaringan Akomodasi Luas",
    defaultDescription: "Kami memiliki kontrak dengan berbagai hotel dari bintang 3 hingga 5 dan provider transportasi terpercaya untuk land arrangement umrah terbaik."
  },
];

const Features = () => {
  const { translations } = useContext(LanguageContext);
  const [featuresData, setFeaturesData] = useState(defaultFeatures.map(f => ({...f, title: f.defaultTitle, description: f.defaultDescription })));

  const getTranslation = (key, fallback) => translations[key] || fallback;

  useEffect(() => {
    const storedContent = localStorage.getItem('homePageContent');
    if (storedContent) {
      const parsedContent = JSON.parse(storedContent);
      if (parsedContent.features && Array.isArray(parsedContent.features) && parsedContent.features.length > 0) {
        const cmsFeatures = defaultFeatures.map(defaultFeature => {
          const cmsFeature = parsedContent.features.find(cf => cf.id === defaultFeature.id);
          return {
            ...defaultFeature,
            title: cmsFeature?.title || defaultFeature.defaultTitle,
            description: cmsFeature?.description || defaultFeature.defaultDescription,
          };
        });
        setFeaturesData(cmsFeatures);
      } else {
         setFeaturesData(defaultFeatures.map(f => ({...f, title: getTranslation(f.titleKey, f.defaultTitle), description: getTranslation(f.descriptionKey, f.defaultDescription) })));
      }
    } else {
      setFeaturesData(defaultFeatures.map(f => ({...f, title: getTranslation(f.titleKey, f.defaultTitle), description: getTranslation(f.descriptionKey, f.defaultDescription) })));
    }
  }, [translations]);


  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { type: "spring", stiffness: 100, damping: 12 }
    },
  };

  return (
    <section className="py-20 bg-secondary">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
            {getTranslation('featuresTitle', 'Keunggulan Layanan Handling Umrah Kami')}
          </h2>
          <p className="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto">
            {getTranslation('featuresSubtitle', 'Kami tidak hanya menyediakan jasa handling umroh, kami memberikan jaminan kualitas, keamanan, dan ketenangan untuk Anda dan jamaah.')}
          </p>
          <div className="w-24 h-1.5 bg-linear-to-r from-[#FFD700] to-[#FFA500] mx-auto rounded-full mt-4"></div>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {featuresData.map((feature, index) => (
            <motion.div 
              key={feature.id || index}
              className="bg-linear-to-br from-gray-800 to-gray-900 p-8 rounded-xl shadow-2xl border border-gray-700/50 text-center transition-all duration-300 ease-in-out hover:shadow-glow hover:border-[#FFD700]/50 transform hover:-translate-y-2"
              variants={itemVariants}
            >
              <div className="flex justify-center items-center mb-6">
                {feature.icon}
              </div>
              <h3 className="text-2xl font-bold text-white mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-400 leading-relaxed text-sm">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Features;
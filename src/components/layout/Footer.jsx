import React, { useContext } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Instagram, Twitter, MapPin, Phone, Mail, Building } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const WhatsAppIcon = ({ className = "h-5 w-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M16.6,14.2l-1.5-0.7c-0.2-0.1-0.5-0.1-0.7,0.1l-0.8,0.9c-0.2,0.2-0.5,0.3-0.8,0.2C11.6,14,10,12.5,9.2,11.2 c-0.1-0.2-0.1-0.5,0.1-0.7l0.7-0.7c0.2-0.2,0.2-0.5,0.1-0.7l-0.7-1.5C8.3,7.4,8,7.3,7.8,7.3L6.2,7.3C5.7,7.3,5.3,7.7,5.3,8.1 c0,0.1,0,0.2,0,0.3c0.4,2.5,1.8,4.7,3.9,6.5c2,1.8,4.3,2.9,6.9,3.1c0.1,0,0.2,0,0.3,0c0.5,0,0.8-0.4,0.8-0.8l0-1.6 C17,14.5,16.8,14.3,16.6,14.2z M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10c5.5,0,10-4.5,10-10S17.5,2,12,2z M12,20.5 c-4.7,0-8.5-3.8-8.5-8.5S7.3,3.5,12,3.5s8.5,3.8,8.5,8.5S16.7,20.5,12,20.5z"/>
  </svg>
);

const TikTokIcon = ({ className = "h-5 w-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-2.47.03-4.8-.73-6.56-2.34-1.45-1.34-2.28-3.05-2.48-4.9-.06-1.72.02-3.44-.02-5.16-.04-1.58-.48-3.16-1.2-4.57-.88-1.7-.49-3.73.8-5.09 1.34-1.42 3.11-2.12 5.04-2.04.83.03 1.66.03 2.48.03Zm0 2.79c-.81 0-1.61.01-2.42.01-1.09.01-2.18.31-3.14.85-.53.29-.93.7-1.22 1.18-.29.47-.44.99-.52 1.52-.07.52-.02 1.05-.02 1.57 0 2.83-.01 5.66-.01 8.49.01.82.21 1.63.55 2.37.47 1.04 1.25 1.86 2.22 2.44.99.59 2.12.88 3.28.83.97-.04 1.92-.34 2.76-.87.53-.33.96-.76 1.3-1.25.29-.41.51-.85.66-1.31.17-.53.28-1.08.32-1.64.04-.6.02-1.2.02-1.81.01-2.82 0-5.65.01-8.48Zm3.86 1.44c-.02.02-.02.02 0 0Z"/>
  </svg>
);

const FacebookIcon = ({ className = "h-5 w-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm3.5 8h-2.5V8.5c0-.55.45-1 1-1h1.5V5h-2.5C10.56 5 9 6.57 9 8.5v1.5H7v2h2v6h3v-6h2l.5-2z"/>
  </svg>
);


const Footer = () => {
  const { translations } = useContext(LanguageContext);
  const currentYear = new Date().getFullYear();
  const logoUrlFromStorage = localStorage.getItem('siteLogo') || "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e2226538273674d2415bfb7f2ef1cba1.png";

  const navLinks = [
    { nameKey: 'home', path: '/' },
    { nameKey: 'services', path: '/services' },
    { nameKey: 'pricing', path: '/pricing' },
    { nameKey: 'about', path: '/about' },
    { nameKey: 'Blog', path: '/blog' },
    { nameKey: 'faq', path: '/faq' },
    { nameKey: 'contact', path: '/contact' },
  ];
  
  const quickLinks = [
      { nameKey: 'order', path: '/order' },
      ...navLinks,
  ];
  
  const serviceLinks = [
    { nameKey: 'Land Arrangement', path: '/land-arrangement-umrah' },
    { nameKey: 'service1Title', path: '/services#umrah-b2b' },
    { nameKey: 'service2Title', path: '/services#hajj-no-queue' },
    { nameKey: 'service3Title', path: '/services#handling' },
    { nameKey: 'service4Title', path: '/services#visa' },
  ];

  const socialLinks = [
    { icon: <WhatsAppIcon className="h-5 w-5" />, href: translations.socialWhatsAppUrl || "https://wa.me/6281280908093", label: "WhatsApp" },
    { icon: <Instagram className="h-5 w-5" />, href: translations.socialInstagramUrl || "https://www.instagram.com/umrohserviceco/", label: "Instagram" },
    { icon: <FacebookIcon className="h-5 w-5" />, href: translations.socialFacebookUrl || "https://facebook.com/umrohserviceco/", label: "Facebook" },
    { icon: <TikTokIcon className="h-5 w-5" />, href: translations.socialTikTokUrl || "https://www.tiktok.com/@umrahserviceco", label: "TikTok" },
    { icon: <Twitter className="h-5 w-5" />, href: translations.socialTwitterUrl || "https://x.com/umrahserviceco", label: "Twitter/X" },
  ];

  const getTranslatedText = (key, fallback) => {
      if (key === 'order') return translations['bookNow'] || "Pesan Sekarang";
      const translated = translations[key];
      return translated || fallback || key;
  };
  
  const footerRightsText = (getTranslatedText('footerRights', "© {year} Arrahmah Handling Service. Hak Cipta Dilindungi.")).replace('{year}', currentYear);


  return (
    <footer className="bg-linear-to-b from-secondary to-background text-gray-300 pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          
          <div className="space-y-6">
            <Link to="/" className="inline-block mb-2">
              <img src={logoUrlFromStorage} alt={getTranslatedText('logoAlt', 'Logo Arrahmah Handling Service')} className="h-16 w-auto" />
            </Link>
            <p className="text-sm leading-relaxed opacity-80">
              {getTranslatedText('heroSubtitle', 'Mitra terpercaya untuk perjalanan umrah dan haji Anda.')}
            </p>
            <div className="flex space-x-3 mt-8">
              {socialLinks.map(social => (
                <a 
                  key={social.label}
                  href={social.href} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  aria-label={social.label}
                  className="p-2.5 bg-gray-700/50 rounded-full hover:bg-[#FFD700] hover:text-black transition-all duration-300"
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          <div>
            <p className="text-lg font-semibold text-white mb-6">{getTranslatedText('footerServices', 'Layanan')}</p>
            <ul className="space-y-3">
              {serviceLinks.map(link => (
                 <li key={link.path}><Link to={link.path} className="footer-link">{getTranslatedText(link.nameKey)}</Link></li>
              ))}
               <li><Link to="/services#accommodation" className="footer-link">{getTranslatedText('service5Title', 'Reservasi Akomodasi')}</Link></li>
               <li><Link to="/services#transportation" className="footer-link">{getTranslatedText('service6Title', 'Transportasi Jamaah')}</Link></li>
               <li><Link to="/services#catering" className="footer-link">{getTranslatedText('service7Title', 'Katering Jamaah')}</Link></li>
               <li><Link to="/services#saudi-tour" className="footer-link">{getTranslatedText('service8Title', 'Paket Tour Arab Saudi')}</Link></li>
            </ul>
          </div>

          <div>
            <p className="text-lg font-semibold text-white mb-6">{getTranslatedText('quickLinks', 'Tautan Cepat')}</p>
            <ul className="space-y-3">
              {quickLinks.map(link => (
                 <li key={link.path}><Link to={link.path} className="footer-link">{getTranslatedText(link.nameKey, link.nameKey)}</Link></li>
              ))}
              <li><a href={getTranslatedText('loginTravelUrl', 'https://travel.umrahservice.co')} target="_blank" rel="noopener noreferrer" className="footer-link">{getTranslatedText('loginTravel', 'Login Travel')}</a></li>
            </ul>
          </div>
          
          <div>
            <p className="text-lg font-semibold text-white mb-6">{getTranslatedText('footerContactUs', 'Hubungi')}</p>
            <ul className="space-y-4">
              <li className="flex items-start">
                <Building className="h-5 w-5 mr-3 mt-1 text-[#FFD700] shrink-0" />
                <div>
                  <p className="font-medium text-gray-100">{getTranslatedText('contactIndonesiaOffice', 'Kantor Indonesia')}</p>
                  <p className="text-sm">Grand Galaxy Park, Ruko No. 81 Blok RGA, Jaka Setia, Kec. Bekasi Selatan, Kota Bekasi, Jawa Barat 17147</p>
                </div>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-3 text-[#FFD700]" />
                <a href={`tel:${getTranslatedText('contactPhoneIndonesia', '+6281280908093').replace(/\s|\(|\)/g, '')}`} className="footer-link">{getTranslatedText('contactPhoneIndonesia', '+62 (812) 8090 8093')}</a>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-3 text-[#FFD700]" />
                <a href={`mailto:${getTranslatedText('contactEmail1', '<EMAIL>')}`} className="footer-link">{getTranslatedText('contactEmail1', '<EMAIL>')}</a>
              </li>
            </ul>
          </div>

        </div>
        
        <div className="border-t border-gray-700 pt-8 mt-8 text-center">
          <p className="text-sm opacity-70">{footerRightsText}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
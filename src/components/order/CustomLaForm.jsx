import React from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Layers, Wrench, PackagePlus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';


const CustomLaForm = () => {
    const navigate = useNavigate();

    return (
        <Card className="bg-linear-to-br from-amber-500/10 via-background to-background border-amber-400/30 shadow-lg shadow-amber-500/5">
            <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-amber-400">Rancang Paket Impian Anda</CardTitle>
                <CardDescription className="text-gray-300">
                    Buat sendiri paket Land Arrangement yang sesuai dengan kebutuhan travel Anda.
                </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center space-y-4 pt-4">
                 <Button 
                    size="lg" 
                    className="w-full max-w-sm gold-gradient text-black font-bold text-lg py-6"
                    onClick={() => navigate('/custom-la-order')}
                >
                    <Wrench className="mr-3 h-6 w-6" />
                    Buat Custom Paket LA
                </Button>
            </CardContent>
        </Card>
    );
};

export default CustomLaForm;
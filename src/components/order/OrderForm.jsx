import React from 'react';
import BookerInfoSection from '@/components/order/sections/BookerInfoSection';
import PackageTypeSection from '@/components/order/sections/PackageTypeSection';
import PackagePricingSection from '@/components/order/sections/PackagePricingSection';
import OptionalAddonsSection from '@/components/order/sections/OptionalAddonsSection';
import FlightDetailsSection from '@/components/order/sections/FlightDetailsSection';
import HotelDetailsSection from '@/components/order/sections/HotelDetailsSection';
import VisaStatusSection from '@/components/order/sections/VisaStatusSection';
import LaB2bSelectionSection from '@/components/order/sections/LaB2bSelectionSection';
import RoomCompositionSection from '@/components/order/sections/RoomCompositionSection';
import DocumentUploadSection from '@/components/order/sections/DocumentUploadSection';
import NotesSection from '@/components/order/sections/NotesSection';

const OrderForm = ({ formData, handleInputChange, setFormData, onPackageTypeChange, onSelectLaB2bPackage, setHoveredPackage }) => {
    
    const renderSectionsByPackageType = () => {
        const commonSections = (
            <>
                <DocumentUploadSection
                    data={formData.documents}
                    handleInputChange={(value) => handleInputChange('documents', null, value)}
                />
                <NotesSection
                    data={formData.notes}
                    handleInputChange={(value) => handleInputChange('notes', null, value)}
                />
            </>
        );

        switch(formData.packageType) {
            case 'la_b2b':
                return (
                    <>
                        <LaB2bSelectionSection
                            onSelectPackage={onSelectLaB2bPackage}
                            selectedPackage={formData.selectedLaB2bPackage}
                            setHoveredPackage={setHoveredPackage}
                        />
                        <RoomCompositionSection
                            data={formData.roomComposition}
                            handleInputChange={(field, value) => handleInputChange('roomComposition', field, value)}
                            totalPax={formData.totalPax}
                        />
                        <FlightDetailsSection
                            data={formData.flightDetails}
                            handleInputChange={(field, value) => handleInputChange('flightDetails', field, value)}
                            setFormData={setFormData}
                        />
                         <OptionalAddonsSection
                            data={formData}
                            handleInputChange={handleInputChange}
                            setFormData={setFormData}
                            packageType={formData.packageType}
                        />
                        {commonSections}
                    </>
                );
            case 'handling_service':
            case 'bundling_visa_handling':
                return (
                    <>
                        <RoomCompositionSection
                            data={formData.roomComposition}
                            handleInputChange={(field, value) => handleInputChange('roomComposition', field, value)}
                             totalPax={formData.totalPax}
                        />
                        <PackagePricingSection
                            packageType={formData.packageType}
                            pricingData={formData.packagePricing}
                            handleInputChange={(field, value) => handleInputChange('packagePricing', field, value)}
                            totalPax={formData.totalPax}
                        />
                        <FlightDetailsSection
                            data={formData.flightDetails}
                            handleInputChange={(field, value) => handleInputChange('flightDetails', field, value)}
                            setFormData={setFormData}
                        />
                        <HotelDetailsSection
                            data={formData.hotelDetails}
                            setFormData={setFormData}
                        />
                         {formData.packageType === 'handling_service' && (
                            <VisaStatusSection
                                value={formData.visaStatus}
                                handleInputChange={(value) => handleInputChange('visaStatus', null, value)}
                            />
                        )}
                        <OptionalAddonsSection
                            data={formData}
                            handleInputChange={handleInputChange}
                            setFormData={setFormData}
                            packageType={formData.packageType}
                        />
                        {commonSections}
                    </>
                );
            case 'handling_airport_only':
                return (
                     <>
                        <RoomCompositionSection
                            data={formData.roomComposition}
                            handleInputChange={(field, value) => handleInputChange('roomComposition', field, value)}
                             totalPax={formData.totalPax}
                        />
                        <PackagePricingSection
                            packageType={formData.packageType}
                            pricingData={formData.packagePricing}
                            handleInputChange={(field, value) => handleInputChange('packagePricing', field, value)}
                            totalPax={formData.totalPax}
                        />
                        <FlightDetailsSection
                            data={formData.flightDetails}
                            handleInputChange={(field, value) => handleInputChange('flightDetails', field, value)}
                            setFormData={setFormData}
                        />
                         <OptionalAddonsSection
                            data={formData}
                            handleInputChange={handleInputChange}
                            setFormData={setFormData}
                            packageType={formData.packageType}
                        />
                        {commonSections}
                    </>
                );
            default:
                return null;
        }
    };


    return (
        <div className="space-y-8">
            <BookerInfoSection
                data={formData.bookerInfo}
                handleInputChange={(field, value) => handleInputChange('bookerInfo', field, value)}
            />
            <PackageTypeSection
                value={formData.packageType}
                onValueChange={onPackageTypeChange}
            />
            
            {renderSectionsByPackageType()}
        </div>
    );
};

export default OrderForm;
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { FileText, Hotel, Plane, Briefcase, Package, Star } from 'lucide-react';
import { serviceOptions } from '@/pages/OrderPage.jsx';
import VisaFormSection from './details/VisaFormSection';
import HotelFormSection from './details/HotelFormSection';
import AirportHandlingIdFormSection from './details/AirportHandlingIdFormSection';
import AirportHandlingSaFormSection from './details/AirportHandlingSaFormSection';
import GroundHandlingFormSection from './details/GroundHandlingFormSection';
import PifServiceFormSection from './details/PifServiceFormSection';

const OrderFormDetails = ({ formData, setFormData, isFullLaActive }) => {
    const handleInputChange = (section, field, value) => {
        setFormData(prev => ({
            ...prev,
            [section]: {
                ...prev[section],
                [field]: value
            }
        }));
    };

    const renderServiceIcon = (service) => {
        switch(service) {
            case serviceOptions.APPLY_VISA: return <FileText className="h-5 w-5 text-amber-400 mr-3" />;
            case serviceOptions.BOOK_HOTEL: return <Hotel className="h-5 w-5 text-amber-400 mr-3" />;
            case serviceOptions.HANDLING_AIRPORT_ID: return <Plane className="h-5 w-5 text-amber-400 mr-3" />;
            case serviceOptions.HANDLING_AIRPORT_SA: return <Plane className="h-5 w-5 text-amber-400 mr-3" />;
            case serviceOptions.GROUND_HANDLING: return <Briefcase className="h-5 w-5 text-amber-400 mr-3" />;
            case serviceOptions.FULL_HANDLING: return <Package className="h-5 w-5 text-amber-400 mr-3" />;
            case serviceOptions.PIF_SERVICE: return <Star className="h-5 w-5 text-amber-400 mr-3" />;
            default: return null;
        }
    };
    
    const renderFormSection = (service) => {
        switch(service) {
            case serviceOptions.APPLY_VISA:
                return <VisaFormSection visaData={formData.visa} onInputChange={(field, value) => handleInputChange('visa', field, value)} />;
            case serviceOptions.BOOK_HOTEL:
                return <HotelFormSection hotelsData={formData.hotels} setFormData={setFormData} />;
            case serviceOptions.HANDLING_AIRPORT_ID:
                return <AirportHandlingIdFormSection data={formData.handlingAirportId} onInputChange={(field, value) => handleInputChange('handlingAirportId', field, value)} />;
            case serviceOptions.HANDLING_AIRPORT_SA:
                return <AirportHandlingSaFormSection data={formData.handlingAirportSa} onInputChange={(field, value) => handleInputChange('handlingAirportSa', field, value)} />;
            case serviceOptions.GROUND_HANDLING:
                return <GroundHandlingFormSection data={formData.groundHandling} onInputChange={(field, value) => handleInputChange('groundHandling', field, value)} />;
            case serviceOptions.FULL_HANDLING:
                return <p>Layanan ini aktif jika Anda memilih Visa, Hotel, dan salah satu jenis Handling. Detail akan dikumpulkan dari bagian-bagian tersebut.</p>;
            case serviceOptions.PIF_SERVICE:
                return <PifServiceFormSection pifServices={formData.pifServices} setFormData={setFormData} />;
            default: return null;
        }
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle>Detail Layanan</CardTitle>
                <CardDescription>Lengkapi detail untuk layanan yang Anda pilih.</CardDescription>
            </CardHeader>
            <CardContent>
                <Accordion type="multiple" className="w-full space-y-4" defaultValue={formData.selectedServices}>
                    {Object.values(serviceOptions).filter(s => formData.selectedServices.includes(s)).map(service => (
                       <AccordionItem value={service} key={service} className="bg-gray-800/50 border border-gray-700 rounded-lg overflow-hidden">
                           <AccordionTrigger className="p-4 text-white hover:no-underline">
                               <div className="flex items-center">
                                   {renderServiceIcon(service)}
                                   {service}
                                   {service === serviceOptions.FULL_HANDLING && isFullLaActive && 
                                     <Badge className="ml-3 bg-green-500 text-white">Full LA Aktif</Badge>
                                   }
                               </div>
                           </AccordionTrigger>
                           <AccordionContent className="p-4 border-t border-gray-700">
                               {renderFormSection(service)}
                           </AccordionContent>
                       </AccordionItem>
                    ))}
                </Accordion>
            </CardContent>
        </Card>
    );
};

export default OrderFormDetails;
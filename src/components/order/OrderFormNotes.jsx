import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

const OrderFormNotes = ({ formData, setFormData }) => {
    const handleNotesChange = (value) => {
        setFormData(prev => ({ ...prev, notes: value }));
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader><CardTitle>Catatan Tambahan</CardTitle></CardHeader>
            <CardContent>
                <Textarea 
                    placeholder="Tuliskan permintaan atau catatan khusus di sini..." 
                    value={formData.notes} 
                    onChange={e => handleNotesChange(e.target.value)} 
                />
            </CardContent>
        </Card>
    );
};

export default OrderFormNotes;
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { serviceOptions } from '@/pages/OrderPage.jsx';

const OrderFormServices = ({ formData, setFormData }) => {
    const handleServiceChange = (service, checked) => {
        setFormData(prev => {
            const newServices = checked
                ? [...prev.selectedServices, service]
                : prev.selectedServices.filter(s => s !== service);
            return { ...prev, selectedServices: newServices };
        });
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle>Pilih <PERSON>anan</CardTitle>
                <CardDescription>Pilih satu atau lebih layanan yang Anda butuhkan.</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {Object.values(serviceOptions).map(service => (
                    <div key={service} className="flex items-center space-x-3 bg-gray-900/50 p-3 rounded-md">
                        <Checkbox 
                            id={service} 
                            checked={formData.selectedServices.includes(service)} 
                            onCheckedChange={(checked) => handleServiceChange(service, checked)}
                        />
                        <Label htmlFor={service} className="text-base text-white cursor-pointer">{service}</Label>
                    </div>
                ))}
            </CardContent>
        </Card>
    );
};

export default OrderFormServices;
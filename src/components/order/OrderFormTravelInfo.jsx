import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const OrderFormTravelInfo = ({ formData, setFormData }) => {
    const handleInputChange = (section, field, value) => {
        setFormData(prev => ({
            ...prev,
            [section]: {
                ...prev[section],
                [field]: value
            }
        }));
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader><CardTitle>Informasi Travel</CardTitle></CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="travelName">Nama Travel</Label>
                    <Input 
                        id="travelName" 
                        placeholder="Nama Travel Anda" 
                        value={formData.travelInfo.travelName} 
                        onChange={e => handleInputChange('travelInfo', 'travelName', e.target.value)} 
                        required 
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="picName">Nama Penanggung Jawab</Label>
                    <Input 
                        id="picName" 
                        placeholder="Nama Anda" 
                        value={formData.travelInfo.picName} 
                        onChange={e => handleInputChange('travelInfo', 'picName', e.target.value)} 
                        required 
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="whatsapp">No. WhatsApp</Label>
                    <Input 
                        id="whatsapp" 
                        type="tel" 
                        placeholder="081234567890" 
                        value={formData.travelInfo.whatsapp} 
                        onChange={e => handleInputChange('travelInfo', 'whatsapp', e.target.value)} 
                        required 
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input 
                        id="email" 
                        type="email" 
                        placeholder="<EMAIL>" 
                        value={formData.travelInfo.email} 
                        onChange={e => handleInputChange('travelInfo', 'email', e.target.value)} 
                        required 
                    />
                </div>
            </CardContent>
        </Card>
    );
};

export default OrderFormTravelInfo;
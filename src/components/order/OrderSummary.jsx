import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Package, Users, DollarSign, PlusCircle, Bus, CalendarDays } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { calculateNights } from '@/lib/dateUtils';

const SummaryItem = ({ label, value, isBold = false, isLarge = false, isCurrency = false, className = '' }) => (
    <div className={`flex justify-between items-center ${className}`}>
        <p className="text-gray-300">{label}</p>
        <p className={`${isBold ? 'font-bold text-white' : 'text-gray-200'} ${isLarge ? 'text-lg' : 'text-base'}`}>
            {isCurrency ? `${value}` : value}
        </p>
    </div>
);

const OrderSummary = ({ summary, formData }) => {
    const formatCurrency = (value) => {
        return (value || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };
    
    const formatCurrencyNoCents = (value) => {
        return (value || 0).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
    };

    const hasGroupDetails = summary.groupDetails && summary.groupDetails.length > 0;
    
    const totalHotelNights = formData?.hotelDetails?.hotels?.reduce((acc, hotel) => acc + (parseInt(hotel.nights, 10) || 0), 0) || 0;
    const showHotelDuration = (formData?.packageType === 'handling_service' || formData?.packageType === 'bundling_visa_handling') && totalHotelNights > 0;

    return (
        <Card className="bg-gray-900/80 border-gray-700 shadow-xl backdrop-blur-xs">
            <CardHeader>
                <CardTitle className="text-xl text-amber-400">Ringkasan Estimasi Harga</CardTitle>
                <CardDescription>Harga dihitung otomatis berdasarkan input Anda.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="p-4 bg-gray-800/50 rounded-lg space-y-3">
                    <SummaryItem label="Paket Dipilih" value={summary.packageName || 'N/A'} isBold />
                    <div className="flex justify-between items-center">
                        <p className="text-gray-300">Jumlah Jamaah</p>
                        <Badge variant="secondary" className="text-lg">{summary.totalPax} Pax</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                        <p className="text-gray-300">Jumlah Bus</p>
                        <Badge variant="outline" className="text-lg flex items-center"><Bus className="h-4 w-4 mr-2"/>{summary.groups} Bus</Badge>
                    </div>
                    {showHotelDuration && (
                         <div className="flex justify-between items-center">
                            <p className="text-gray-300">Total Durasi Hotel</p>
                            <Badge variant="outline" className="text-lg flex items-center"><CalendarDays className="h-4 w-4 mr-2"/>{totalHotelNights} Malam</Badge>
                        </div>
                    )}
                    {summary.pricePerPax > 0 && <SummaryItem label="Harga rata-rata / Pax" value={formatCurrency(summary.pricePerPax)} isCurrency />}
                    {summary.subtotal > 0 && <Separator className="bg-gray-600" />}
                    <SummaryItem label="Subtotal Paket" value={formatCurrency(summary.subtotal)} isBold isCurrency />
                </div>
                
                {hasGroupDetails && (
                     <Accordion type="single" collapsible className="w-full" defaultValue="group-details">
                        <AccordionItem value="group-details" className="border-none">
                            <AccordionTrigger className="text-sm text-amber-300 hover:no-underline p-2 bg-gray-800/50 rounded-md">Lihat Rincian per Bus</AccordionTrigger>
                            <AccordionContent className="pt-3 text-gray-300 text-sm space-y-2">
                                {summary.groupDetails.map(group => (
                                    <div key={group.bus} className="p-3 border border-gray-700 rounded-md bg-gray-900/30">
                                        <div className="flex justify-between items-center font-bold">
                                            <span>Bus {group.bus}</span>
                                            <span>{group.pax} Pax</span>
                                        </div>
                                        <Separator className="my-2 bg-gray-600" />
                                        <div className="flex justify-between items-center text-xs">
                                            <span>Harga/Pax:</span>
                                            <span>${formatCurrencyNoCents(group.price_per_pax)}</span>
                                        </div>
                                         <div className="flex justify-between items-center text-xs text-amber-300">
                                            <span>Total Grup:</span>
                                            <span className="font-bold">${formatCurrency(group.total_group_price)}</span>
                                        </div>
                                    </div>
                                ))}
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                )}

                {summary.addons.length > 0 && (
                    <div className="p-4 bg-gray-800/50 rounded-lg space-y-3">
                        <div className="flex items-center font-semibold text-white">
                            <PlusCircle className="h-5 w-5 mr-2 text-amber-400"/>
                            Layanan Tambahan
                        </div>
                        <Separator className="bg-gray-700" />
                        {summary.addons.map(addon => (
                            <div key={addon.name}>
                                <SummaryItem label={addon.name} value={formatCurrency(addon.cost)} isCurrency />
                                {addon.perPax && (
                                    <p className="text-xs text-gray-400 text-right -mt-1">(${formatCurrency(addon.perPax)}/pax)</p>
                                )}
                            </div>
                        ))}
                    </div>
                )}
                
                <Separator className="my-2 bg-gray-600" />

                <div className="p-4 bg-black/30 rounded-lg">
                    <div className="flex justify-between items-center">
                        <p className="text-lg font-bold text-white">Estimasi Total</p>
                        <p className="text-2xl font-bold text-amber-400">${formatCurrency(summary.total)}</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default OrderSummary;
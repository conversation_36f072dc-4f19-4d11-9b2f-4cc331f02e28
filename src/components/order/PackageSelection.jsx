import React, { useState, useContext } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Package, Check, Edit } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { packagesData } from '@/components/pricing/la_b2b/packagesData';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useNavigate } from 'react-router-dom';

const PackageCard = ({ pkg, onSelect, isSelected, onMouseEnter, onMouseLeave }) => (
    <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        onClick={() => onSelect(pkg)}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        className={`relative p-4 border rounded-lg cursor-pointer transition-all duration-300 h-full flex flex-col ${isSelected ? 'border-amber-400 bg-amber-500/10 shadow-lg' : 'border-gray-700 bg-gray-800/50 hover:border-amber-400'}`}
    >
        {isSelected && (
            <div className="absolute top-2 right-2 bg-amber-400 text-black rounded-full p-1">
                <Check className="h-4 w-4" />
            </div>
        )}
        <h4 className="font-bold text-white grow">{pkg.name}</h4>
        <p className="text-sm text-gray-400 mt-1">{pkg.duration}</p>
        <div className="flex items-center text-xs text-amber-300 mt-2">
            {[...Array(pkg.stars)].map((_, i) => <Check key={i} className="h-3 w-3 mr-0.5" />)} Bintang {pkg.stars}
        </div>
    </motion.div>
);

const PackageSelection = ({ onSelectPackage, selectedPackage, setHoveredPackage }) => {
    const { translations } = useContext(LanguageContext);
    const [searchTerm, setSearchTerm] = useState('');
    const navigate = useNavigate();

    const allPackages = [...packagesData.program10Nights, ...packagesData.program7Nights];

    const filteredPackages = allPackages.filter(pkg => 
        pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.hotelMadinah.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.hotelMakkah.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleCustomFormToggle = () => {
        navigate('/custom-la-order');
    }

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle>Pilih Paket Land Arrangement (LA) B2B</CardTitle>
                <CardDescription>Pilih paket untuk mengisi form secara otomatis, atau buat paket custom Anda sendiri.</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-4">
                    <div className="relative grow">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                        <Input 
                            placeholder="Cari nama paket atau hotel..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                    <Button variant="outline" onClick={handleCustomFormToggle}>
                        <Edit className="mr-2 h-4 w-4"/>
                        Custom Paket LA B2B
                    </Button>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto p-1">
                    <AnimatePresence>
                        {filteredPackages.map(pkg => (
                            <PackageCard 
                                key={pkg.id} 
                                pkg={pkg} 
                                onSelect={onSelectPackage}
                                isSelected={selectedPackage?.id === pkg.id}
                                onMouseEnter={() => setHoveredPackage(pkg)}
                                onMouseLeave={() => setHoveredPackage(null)}
                            />
                        ))}
                    </AnimatePresence>
                </div>
            </CardContent>
        </Card>
    );
};

export default PackageSelection;
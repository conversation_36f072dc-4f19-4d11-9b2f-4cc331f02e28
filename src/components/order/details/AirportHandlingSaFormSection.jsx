import React from 'react';
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar as CalendarIcon } from 'lucide-react';

const AirportHandlingSaFormSection = ({ data, onInputChange }) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
             <div className="space-y-2">
                <Label>Kota Bandara</Label>
                <Select value={data.airport} onValueChange={v => onInputChange('airport', v)}>
                    <SelectTrigger><SelectValue placeholder="Pilih bandara" /></SelectTrigger>
                    <SelectContent>
                        <SelectItem value="Jeddah">Jeddah</SelectItem>
                        <SelectItem value="Madinah">Madinah</SelectItem>
                        <SelectItem value="Thaif">Thaif</SelectItem>
                        <SelectItem value="Al Ula">Al Ula</SelectItem>
                    </SelectContent>
                </Select>
            </div>
            <div className="space-y-2">
                <Label>Jumlah Jamaah</Label>
                <Input type="number" placeholder="Contoh: 45" value={data.paxCount} onChange={e => onInputChange('paxCount', e.target.value)}/>
            </div>
            <div className="space-y-2">
                <Label>Tanggal Pergi</Label>
                <Popover>
                    <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {data.departureDate ? format(data.departureDate, "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                        <Calendar mode="single" selected={data.departureDate} onSelect={(date) => onInputChange('departureDate', date)}/>
                    </PopoverContent>
                </Popover>
            </div>
            <div className="space-y-2">
                <Label>Tanggal Pulang</Label>
                <Popover>
                    <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {data.returnDate ? format(data.returnDate, "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                        <Calendar mode="single" selected={data.returnDate} onSelect={(date) => onInputChange('returnDate', date)}/>
                    </PopoverContent>
                </Popover>
            </div>
            <div className="flex items-center space-x-2">
                <Checkbox id="mealBoxSa" checked={data.mealBox} onCheckedChange={(c) => onInputChange('mealBox', c)} />
                <Label htmlFor="mealBoxSa">Nasi Box</Label>
            </div>
            {data.mealBox && <Input type="number" placeholder="Jumlah" value={data.mealBoxCount} onChange={e => onInputChange('mealBoxCount', e.target.value)}/>}
            <div className="flex items-center space-x-2">
                <Checkbox id="snackSa" checked={data.snack} onCheckedChange={(c) => onInputChange('snack', c)} />
                <Label htmlFor="snackSa">Snack</Label>
            </div>
            {data.snack && <Input type="number" placeholder="Jumlah" value={data.snackCount} onChange={e => onInputChange('snackCount', e.target.value)}/>}
        </div>
    );
};

export default AirportHandlingSaFormSection;
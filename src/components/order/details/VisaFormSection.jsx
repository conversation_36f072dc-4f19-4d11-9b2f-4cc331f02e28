import React from 'react';
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon } from 'lucide-react';

const VisaFormSection = ({ visaData, onInputChange }) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
                <Label htmlFor="visaDepartureDate">Tanggal Keberangkatan</Label>
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant={"outline-solid"}
                            className="w-full justify-start text-left font-normal"
                        >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {visaData.departureDate ? format(visaData.departureDate, "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                        <Calendar
                            mode="single"
                            selected={visaData.departureDate}
                            onSelect={(date) => onInputChange('departureDate', date)}
                            initialFocus
                        />
                    </PopoverContent>
                </Popover>
            </div>
            <div className="space-y-2">
                <Label htmlFor="visaCount">Jumlah Visa</Label>
                <Input 
                    id="visaCount" 
                    type="number" 
                    placeholder="Contoh: 45" 
                    value={visaData.visaCount} 
                    onChange={e => onInputChange('visaCount', e.target.value)} 
                />
            </div>
        </div>
    );
};

export default VisaFormSection;
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const BookerInfoSection = ({ data, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">1</span>
                    Informasi Pemesan
                </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="travelName">Nama Travel / Agen Umrah</Label>
                    <Input id="travelName" placeholder="Nama Travel Anda" value={data.travelName} onChange={e => handleInputChange('travelName', e.target.value)} required />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="picName">Nama PIC (Penanggung Jawab)</Label>
                    <Input id="picName" placeholder="Nama Anda" value={data.picName} onChange={e => handleInputChange('picName', e.target.value)} required />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="whatsapp">Nomor WhatsApp Aktif</Label>
                    <Input id="whatsapp" type="tel" placeholder="+62..." value={data.whatsapp} onChange={e => handleInputChange('whatsapp', e.target.value)} required />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="email">Email (opsional)</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" value={data.email} onChange={e => handleInputChange('email', e.target.value)} />
                </div>
                <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="jamaahOrigin">Asal Jamaah</Label>
                    <Input id="jamaahOrigin" placeholder="Contoh: Jakarta, Surabaya" value={data.jamaahOrigin} onChange={e => handleInputChange('jamaahOrigin', e.target.value)} />
                </div>
            </CardContent>
        </Card>
    );
};

export default BookerInfoSection;
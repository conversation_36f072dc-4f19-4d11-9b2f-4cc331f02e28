import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from 'lucide-react';

const FlightDetailsSection = ({ data, handleInputChange, setFormData }) => {
    const handleDateChange = (field, date) => {
        setFormData(prev => ({
            ...prev,
            flightDetails: {
                ...prev.flightDetails,
                [field]: date
            }
        }));
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">6</span>
                    Detail Penerbangan
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                <div>
                    <h3 className="font-semibold text-lg text-amber-300 mb-3">Keberangkatan</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label>Tanggal Keberangkatan</Label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                        {data.departureDate ? format(data.departureDate, "PPP") : <span>Pilih tanggal</span>}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={data.departureDate} onSelect={(date) => handleDateChange('departureDate', date)} /></PopoverContent>
                            </Popover>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="departureAirline">Maskapai Keberangkatan</Label>
                            <Input id="departureAirline" value={data.departureAirline} onChange={e => handleInputChange('departureAirline', e.target.value)} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="departureFlightNumber">No. Penerbangan Keberangkatan</Label>
                            <Input id="departureFlightNumber" value={data.departureFlightNumber} onChange={e => handleInputChange('departureFlightNumber', e.target.value)} />
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="font-semibold text-lg text-amber-300 mb-3">Kepulangan</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label>Tanggal Kepulangan</Label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                        {data.returnDate ? format(data.returnDate, "PPP") : <span>Pilih tanggal</span>}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={data.returnDate} onSelect={(date) => handleDateChange('returnDate', date)} /></PopoverContent>
                            </Popover>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="returnAirline">Maskapai Kepulangan</Label>
                            <Input id="returnAirline" value={data.returnAirline} onChange={e => handleInputChange('returnAirline', e.target.value)} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="returnFlightNumber">No. Penerbangan Kepulangan</Label>
                            <Input id="returnFlightNumber" value={data.returnFlightNumber} onChange={e => handleInputChange('returnFlightNumber', e.target.value)} />
                        </div>
                    </div>
                </div>

                <div className="space-y-2 pt-4 border-t border-gray-700">
                    <Label>Status Tiket</Label>
                    <RadioGroup value={data.ticketStatus} onValueChange={(value) => handleInputChange('ticketStatus', value)} className="flex space-x-4">
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Sudah Ada" id="ticket_yes" />
                            <Label htmlFor="ticket_yes">Sudah Ada</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Belum Ada" id="ticket_no" />
                            <Label htmlFor="ticket_no">Belum Ada</Label>
                        </div>
                    </RadioGroup>
                </div>
            </CardContent>
        </Card>
    );
};

export default FlightDetailsSection;
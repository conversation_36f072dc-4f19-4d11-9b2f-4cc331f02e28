import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { Calendar as CalendarIcon, PlusCircle, Trash2 } from 'lucide-react';
import { calculateCheckoutDate } from '@/lib/dateUtils';
import { Badge } from '@/components/ui/badge';

const HotelDetailsSection = ({ data, setFormData }) => {
    const { hotels } = data;

    const handleHotelChange = (id, field, value) => {
        setFormData(prev => {
            const newHotels = prev.hotelDetails.hotels.map(hotel => {
                if (hotel.id === id) {
                    const updatedHotel = { ...hotel, [field]: value };
                    if (field === 'checkIn' || field === 'nights') {
                        updatedHotel.checkOut = calculateCheckoutDate(updatedHotel.checkIn, updatedHotel.nights);
                    }
                    return updatedHotel;
                }
                return hotel;
            });
            return { ...prev, hotelDetails: { ...prev.hotelDetails, hotels: newHotels } };
        });
    };

    const addHotel = () => {
        setFormData(prev => ({
            ...prev,
            hotelDetails: {
                ...prev.hotelDetails,
                hotels: [...prev.hotelDetails.hotels, { id: uuidv4(), hotelName: '', city: 'Makkah', checkIn: null, nights: '', checkOut: null }]
            }
        }));
    };

    const removeHotel = (id) => {
        setFormData(prev => ({
            ...prev,
            hotelDetails: {
                ...prev.hotelDetails,
                hotels: prev.hotelDetails.hotels.filter(hotel => hotel.id !== id)
            }
        }));
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">7</span>
                    Detail Hotel
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {hotels.map((hotel, index) => (
                    <Card key={hotel.id} className="bg-gray-900/50 border-gray-700 p-4">
                        <div className="flex justify-between items-center mb-4">
                            <h4 className="font-semibold text-white">Hotel #{index + 1}</h4>
                            {hotels.length > 1 && (
                                <Button type="button" variant="ghost" size="icon" onClick={() => removeHotel(hotel.id)}>
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                            )}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label>Nama Hotel</Label>
                                <Input value={hotel.hotelName} onChange={e => handleHotelChange(hotel.id, 'hotelName', e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <Label>Kota</Label>
                                <Select value={hotel.city} onValueChange={value => handleHotelChange(hotel.id, 'city', value)}>
                                    <SelectTrigger><SelectValue /></SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Makkah">Makkah</SelectItem>
                                        <SelectItem value="Madinah">Madinah</SelectItem>
                                        <SelectItem value="Jeddah">Jeddah</SelectItem>
                                        <SelectItem value="Taif">Taif</SelectItem>
                                        <SelectItem value="Lainnya">Lainnya</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label>Tanggal Check-in</Label>
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <Button type="button" variant="outline" className="w-full justify-start text-left font-normal">
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {hotel.checkIn ? format(new Date(hotel.checkIn), "PPP") : <span>Pilih tanggal</span>}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={hotel.checkIn ? new Date(hotel.checkIn) : null} onSelect={date => handleHotelChange(hotel.id, 'checkIn', date)} /></PopoverContent>
                                </Popover>
                            </div>
                            <div className="space-y-2">
                                <Label>Jumlah Malam</Label>
                                <Input type="number" value={hotel.nights} onChange={e => handleHotelChange(hotel.id, 'nights', e.target.value)} />
                            </div>
                            <div className="md:col-span-2 space-y-2">
                                <Label>Tanggal Check-out (Otomatis)</Label>
                                <Input value={hotel.checkOut ? format(new Date(hotel.checkOut), "eeee, dd MMMM yyyy") : '...'} readOnly disabled />
                            </div>
                        </div>
                    </Card>
                ))}
                <Button type="button" variant="outline" onClick={addHotel} className="w-full">
                    <PlusCircle className="mr-2 h-4 w-4" /> Tambah Hotel Lagi
                </Button>
            </CardContent>
        </Card>
    );
};

export default HotelDetailsSection;
import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

const MutawwifRequestSection = ({ data, handleInputChange, setFormData }) => {
    const handleNeededChange = (value) => {
        handleInputChange('needed', value);
    };

    const handleCheckboxChange = (group, value) => {
        setFormData(prev => {
            const currentGroup = prev.mutawwifRequest[group] || [];
            const newGroup = currentGroup.includes(value)
                ? currentGroup.filter(item => item !== value)
                : [...currentGroup, value];
            return {
                ...prev,
                mutawwifRequest: {
                    ...prev.mutawwifRequest,
                    [group]: newGroup,
                },
            };
        });
    };

    const styles = ["Sesuai <PERSON>", "Hafal doa ziarah", "Komunikatif"];
    const skills = ["Fotografi", "Videografi", "Bisa MC / Tour Leader", "Bisa Drone"];
    const languages = ["Indonesia", "Arab", "Inggris", "Melayu"];
    const genders = ["Laki-laki", "Perempuan"];

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">9</span>
                    Permintaan Mutawwif
                </CardTitle>
                <CardDescription>Jika diperlukan, akan ada biaya tambahan 250 SR / hari.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <RadioGroup value={data.needed} onValueChange={handleNeededChange} className="flex space-x-4">
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Tidak Perlu" id="mutawwif_no" />
                        <Label htmlFor="mutawwif_no">Tidak perlu mutawwif</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Perlu" id="mutawwif_yes" />
                        <Label htmlFor="mutawwif_yes">Perlu mutawwif</Label>
                    </div>
                </RadioGroup>

                {data.needed === 'Perlu' && (
                    <div className="space-y-4 pt-4 border-t border-gray-700">
                        <div>
                            <Label className="font-semibold">Gaya</Label>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                                {styles.map(item => (
                                    <div key={item} className="flex items-center space-x-2">
                                        <Checkbox id={`style_${item}`} checked={data.style.includes(item)} onCheckedChange={() => handleCheckboxChange('style', item)} />
                                        <Label htmlFor={`style_${item}`}>{item}</Label>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div>
                            <Label className="font-semibold">Kemampuan</Label>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                                {skills.map(item => (
                                    <div key={item} className="flex items-center space-x-2">
                                        <Checkbox id={`skill_${item}`} checked={data.skills.includes(item)} onCheckedChange={() => handleCheckboxChange('skills', item)} />
                                        <Label htmlFor={`skill_${item}`}>{item}</Label>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div>
                            <Label className="font-semibold">Bahasa</Label>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                                {languages.map(item => (
                                    <div key={item} className="flex items-center space-x-2">
                                        <Checkbox id={`lang_${item}`} checked={data.languages.includes(item)} onCheckedChange={() => handleCheckboxChange('languages', item)} />
                                        <Label htmlFor={`lang_${item}`}>{item}</Label>
                                    </div>
                                ))}
                            </div>
                        </div>
                         <div>
                            <Label className="font-semibold">Gender</Label>
                            <RadioGroup value={data.gender} onValueChange={(value) => handleInputChange('gender', value)} className="flex space-x-4 mt-2">
                                {genders.map(item => (
                                    <div key={item} className="flex items-center space-x-2">
                                        <RadioGroupItem value={item} id={`gender_${item}`} />
                                        <Label htmlFor={`gender_${item}`}>{item}</Label>
                                    </div>
                                ))}
                            </RadioGroup>
                        </div>
                         <div>
                            <Label htmlFor="other_criteria" className="font-semibold">Kriteria Lainnya</Label>
                            <Textarea 
                                id="other_criteria"
                                placeholder="Tuliskan permintaan khusus lainnya untuk mutawwif..."
                                value={data.otherCriteria || ''}
                                onChange={(e) => handleInputChange('otherCriteria', e.target.value)}
                                className="mt-2"
                            />
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default MutawwifRequestSection;
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckCircle, Award, Star, Plane, Utensils } from 'lucide-react';
import { pricingStructure } from '@/data/pricingStructure';

const HandlingOption = ({ id, value, label, description, checked, icon }) => (
    <Label
        htmlFor={id}
        className={`flex items-start p-4 border rounded-lg cursor-pointer transition-all duration-300 ${
            checked ? 'border-amber-400 bg-amber-500/10' : 'border-gray-700 bg-gray-900/50 hover:border-amber-400/50'
        }`}
    >
        <RadioGroupItem value={value} id={id} className="mt-1" />
        <div className="grow ml-4">
            <span className="text-lg font-bold text-white flex items-center">
                {icon}
                {label}
            </span>
            <p className="text-gray-400 mt-1">{description}</p>
        </div>
    </Label>
);

const PackagePricingSection = ({ packageType, pricingData, handleInputChange, totalPax }) => {
    const handlingDetails = {
        basic: "Welcome drink (Zamzam + Kurma), handling hotel & airport, koper, snack 4x, dokumentasi, tips porter & driver",
        premium: "Semua fasilitas Basic + X-Banner, Al Baik 1x, snack premium, parcel buah, air mineral",
        vip: "Semua fasilitas Basic + Arabian kuliner Al Romanshiah, snack VIP, parcel VIP, Zamzam galon, Al Baik (Madinah-Makkah)"
    };

    const airportServiceOptions = Object.keys(pricingStructure.handling_airport_only.prices);

    const renderContent = () => {
        switch (packageType) {
            case 'handling_service':
            case 'bundling_visa_handling':
                return (
                    <RadioGroup
                        value={pricingData.category}
                        onValueChange={(value) => handleInputChange('category', value)}
                        className="space-y-4"
                    >
                        <HandlingOption
                            id="handling-basic"
                            value="basic"
                            label="Basic"
                            description={handlingDetails.basic}
                            checked={pricingData.category === 'basic'}
                            icon={<CheckCircle className="mr-2 h-5 w-5 text-gray-400" />}
                        />
                        <HandlingOption
                            id="handling-premium"
                            value="premium"
                            label="Premium"
                            description={handlingDetails.premium}
                            checked={pricingData.category === 'premium'}
                            icon={<Award className="mr-2 h-5 w-5 text-amber-400" />}
                        />
                        <HandlingOption
                            id="handling-vip"
                            value="vip"
                            label="VIP"
                            description={handlingDetails.vip}
                            checked={pricingData.category === 'vip'}
                            icon={<Star className="mr-2 h-5 w-5 text-yellow-300" />}
                        />
                    </RadioGroup>
                );
            case 'handling_airport_only':
                return (
                    <div className="space-y-6">
                        <div className="space-y-2">
                            <Label htmlFor="airport-service-type" className="flex items-center"><Plane className="mr-2 h-4 w-4" />Pilih Jenis Layanan</Label>
                            <Select value={pricingData.airportService} onValueChange={(value) => handleInputChange('airportService', value)}>
                                <SelectTrigger id="airport-service-type">
                                    <SelectValue placeholder="Pilih jenis layanan handling airport" />
                                </SelectTrigger>
                                <SelectContent>
                                    {airportServiceOptions.map(option => (
                                        <SelectItem key={option} value={option}>{option}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-4 p-4 border border-gray-700 rounded-lg">
                             <h4 className="font-semibold text-white text-lg flex items-center"><Utensils className="mr-2 h-5 w-5 text-amber-400" /> Tambahan Al Baik (Opsional)</h4>
                             <div className="space-y-3">
                                <div className="flex items-center space-x-3">
                                    <Checkbox
                                        id="albaik-arrival"
                                        checked={pricingData.alBaikArrival}
                                        onCheckedChange={(checked) => handleInputChange('alBaikArrival', checked)}
                                        disabled={pricingData.airportService?.includes('Kepulangan Saja')}
                                    />
                                    <Label htmlFor="albaik-arrival" className="text-gray-300 font-normal cursor-pointer">Tambahkan Al Baik saat Kedatangan (+$1/pax)</Label>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <Checkbox
                                        id="albaik-departure"
                                        checked={pricingData.alBaikDeparture}
                                        onCheckedChange={(checked) => handleInputChange('alBaikDeparture', checked)}
                                        disabled={pricingData.airportService?.includes('Kedatangan Saja')}
                                    />
                                    <Label htmlFor="albaik-departure" className="text-gray-300 font-normal cursor-pointer">Tambahkan Al Baik saat Kepulangan (+$1/pax)</Label>
                                </div>
                             </div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">4</span>
                    Detail Paket & Harga
                </CardTitle>
                <CardDescription>Pilih kategori paket yang sesuai dengan kebutuhan Anda.</CardDescription>
            </CardHeader>
            <CardContent>
                {renderContent()}
            </CardContent>
        </Card>
    );
};

export default PackagePricingSection;
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

const VisaStatusSection = ({ value, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">8</span>
                    Status Visa
                </CardTitle>
            </CardHeader>
            <CardContent>
                <RadioGroup value={value} onValueChange={handleInputChange} className="flex space-x-4">
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Sudah punya visa" id="visa_have" />
                        <Label htmlFor="visa_have">Sudah punya visa</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Belum, ingin dibantu pengurusan oleh Arrahmah" id="visa_need" />
                        <Label htmlFor="visa_need">Belum, ingin dibantu pengurusan oleh Arrahmah</Label>
                    </div>
                </RadioGroup>
            </CardContent>
        </Card>
    );
};

export default VisaStatusSection;
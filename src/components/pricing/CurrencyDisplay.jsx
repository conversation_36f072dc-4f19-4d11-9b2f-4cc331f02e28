import React from 'react';
import { formatCurrency } from '@/utils/currencyConverter';
import { cn } from '@/lib/utils';

const CurrencyDisplay = ({ amount, currency, lang, className, isLarge = false }) => {
    if (amount === null || isNaN(amount)) {
        return <span className={cn("text-gray-500", className)}>-</span>;
    }

    const { value, unit } = formatCurrency(amount, currency, lang);

    const baseClasses = "font-semibold transition-colors duration-300";
    const sizeClasses = isLarge 
        ? "text-3xl md:text-4xl" 
        : "text-sm text-center";

    if (currency === 'SAR') {
        return (
            <div className={cn('flex items-center justify-center gap-1.5', lang === 'ar' ? 'flex-row-reverse' : 'flex-row', isLarge ? 'gap-2' : 'gap-1.5')}>
                <span className={cn(baseClasses, sizeClasses, "text-inherit", className)}>
                    {value}
                </span>
                <img 
                    src="https://www.sama.gov.sa/ar-sa/Currency/SRS/PublishingImages/Saudi_Riyal_Symbol-1.png" 
                    alt="SR" 
                    className={isLarge ? "h-6 w-auto" : "h-3.5 w-auto"}
                    style={{ filter: 'brightness(0) invert(1)' }}
                />
            </div>
        );
    }
    
    return (
        <span className={cn(baseClasses, sizeClasses, "text-inherit", className)}>
            {value}{unit}
        </span>
    );
};

export default CurrencyDisplay;
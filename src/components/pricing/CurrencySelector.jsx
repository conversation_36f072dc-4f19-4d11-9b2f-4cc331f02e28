import React, { useContext } from 'react';
import { Button } from '@/components/ui/button';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useCurrency } from '@/contexts/CurrencyContext';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

const CurrencySelector = ({ selectedCurrency, onCurrencyChange }) => {
  const { translations, language } = useContext(LanguageContext);
  const { rates, lastRateUpdate } = useCurrency();
  const currencies = ['USD', 'IDR', 'SAR'];

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date)) return '';
    
    let locale = language;
    if(language === 'id') locale = 'id-ID';
    if(language === 'en') locale = 'en-US';
    if(language === 'ar') locale = 'ar-SA';

    return date.toLocaleDateString(locale, {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <div className="flex flex-col items-center justify-center mb-10 space-y-4">
      <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-3">
        <span className="text-lg font-medium text-gray-300 mr-0 sm:mr-3">{translations.currency || 'Currency'}:</span>
        <div className="flex space-x-2 p-1 bg-gray-800 rounded-lg shadow-md">
          {currencies.map((currency) => (
            <Button
              key={currency}
              onClick={() => onCurrencyChange(currency)}
              variant={selectedCurrency === currency ? 'default' : 'ghost'}
              className={cn(
                "px-4 py-2 text-sm font-semibold transition-all duration-200 rounded-md",
                selectedCurrency === currency 
                  ? 'bg-linear-to-r from-[#FFD700] to-[#FFA500] text-black shadow-lg' 
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              )}
            >
              {translations[currency.toLowerCase()] || currency}
            </Button>
          ))}
        </div>
      </div>
      <AnimatePresence>
        {selectedCurrency !== 'USD' && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="text-center"
          >
            <p className="text-sm text-gray-400">
              {translations.exchangeRateReference || 'Acuan Kurs'}:{' '}
              <span className="font-semibold text-amber-400">
                1 USD = {new Intl.NumberFormat(language).format(rates[selectedCurrency])} {selectedCurrency}
              </span>
            </p>
            <p className="text-xs text-gray-500 mt-1">
              ({translations.lastUpdated || 'Terakhir diperbarui'}: {formatDate(lastRateUpdate)})
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CurrencySelector;
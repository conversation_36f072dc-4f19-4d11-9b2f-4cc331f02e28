import React, { useContext, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { Link } from 'react-router-dom';
import { ChevronDown, HelpCircle, MessageSquare } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion.jsx";
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { useContent } from '@/contexts/ContentContext.jsx';

const FAQ = () => {
  const { translations, language } = useContext(LanguageContext);
  const { faqItems: cmsFaqItems, isLoading } = useContent();
  const [faqItems, setFaqItems] = useState([]);

  useEffect(() => {
    if (!isLoading && cmsFaqItems) {
      const langSpecificCmsFaqs = cmsFaqItems.map(item => ({
        id: item.id,
        question: item[`question_${language}`] || item.question_id || item.question,
        answer: item[`answer_${language}`] || item.answer_id || item.answer,
      })).filter(item => item.question && item.answer);

      setFaqItems(langSpecificCmsFaqs);
    }
  }, [cmsFaqItems, isLoading, language]);

  if (isLoading) {
    return (
      <section className="py-20 bg-secondary">
        <div className="container mx-auto px-4 md:px-6 text-center">
          <p className="text-white text-lg">Memuat FAQ...</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-secondary">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <HelpCircle className="w-16 h-16 mx-auto mb-6 text-[#FFD700]" />
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
            {translations.faqTitle || 'Frequently Asked Questions (FAQ)'}
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            {translations.faqSubtitle || 'Pertanyaan Umum Seputar Layanan Arrahmah Handling Service'}
          </p>
          <div className="w-24 h-1.5 bg-linear-to-r from-[#FFD700] to-[#FFA500] mx-auto mt-4 rounded-full"></div>
        </motion.div>

        {faqItems.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.5 }}
            className="max-w-3xl mx-auto"
          >
            <Accordion type="single" collapsible className="w-full space-y-4">
              {faqItems.map((item, index) => (
                <AccordionItem 
                  key={item.id || index} 
                  value={`item-${item.id || index}`} 
                  className="bg-linear-to-br from-gray-800 to-gray-900 border border-gray-700/70 rounded-lg shadow-lg overflow-hidden"
                >
                  <AccordionTrigger className="flex items-center justify-between w-full p-6 text-left text-lg font-medium text-white hover:no-underline hover:bg-white/5 transition-colors">
                    <span>{item.question}</span>
                    <ChevronDown className="h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200" />
                  </AccordionTrigger>
                  <AccordionContent className="p-6 pt-0 text-gray-300 text-base leading-relaxed">
                     <div dangerouslySetInnerHTML={{ __html: item.answer }} />
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </motion.div>
        ) : (
          <motion.p 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-center text-gray-400 text-lg"
          >
            {translations.noFaqsAvailable || "Tidak ada pertanyaan umum yang tersedia saat ini."}
          </motion.p>
        )}
         
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-center mt-16"
        >
            <Button asChild size="lg" className="gold-gradient text-black font-semibold px-8 py-3 text-lg group">
                <Link to="/contact">
                    {translations.faqContactButton || 'Hubungi Kami untuk Info Lebih Lanjut'}
                    <MessageSquare className="ml-2.5 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
            </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQ;
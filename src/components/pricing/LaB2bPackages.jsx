import React, { useContext, useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { LanguageContext } from '@/contexts/LanguageContext';
import { Star, MapPin, CheckCircle2, XCircle, Phone, Users, MessageSquare, Briefcase, ChevronDown, BedDouble, Moon } from 'lucide-react';
import { useCurrency } from '@/contexts/CurrencyContext';
import { convertCurrency } from '@/utils/currencyConverter';
import CurrencyDisplay from '@/components/pricing/CurrencyDisplay';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from '@/lib/utils';

const WHATSAPP_NUMBER = '6281289552018';

const packagesData = {
    program10Nights: [
        {
            id: "b5_fb_10n",
            nameKey: "laB2b_b5_fb_10n_name",
            name: "Bintang 5 (Fullboard)",
            durationKey: "laB2b_10n_duration",
            duration: "Program 10 Malam",
            stars: 5,
            hotelMadinah: "Pullman Zamzam (5 Malam)",
            hotelMakkah: "Pullman Zamzam (5 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 1292, triple: 1469, double: 1821 },
                { pax: "40 - 43", quad: 1303, triple: 1480, double: 1833 },
                { pax: "36 - 39", quad: 1317, triple: 1494, double: 1848 },
                { pax: "32 - 35", quad: 1334, triple: 1511, double: 1866 },
                { pax: "28 - 31", quad: 1355, triple: 1534, double: 1890 },
                { pax: "24 - 27", quad: 1384, triple: 1564, double: 1923 },
                { pax: "20 - 23", quad: 1426, triple: 1607, double: 1968 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_meals_3x", default: "Makan & minum 3x sehari." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_welcome_drink", default: "Welcoming drink zamzam dan kurma saat kedatangan." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
                { key: "include_foc", default: "FOC 1 pax." },
            ],
            excludes: [
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
        {
            id: "b5_bf_10n",
            nameKey: "laB2b_b5_bf_10n_name",
            name: "Bintang 5 (Breakfast)",
            durationKey: "laB2b_10n_duration",
            duration: "Program 10 Malam",
            stars: 5,
            hotelMadinah: "Pullman Zamzam (5 Malam)",
            hotelMakkah: "Pullman Zamzam (5 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 969, triple: 1105, double: 1378 },
                { pax: "40 - 43", quad: 979, triple: 1115, double: 1389 },
                { pax: "36 - 39", quad: 991, triple: 1128, double: 1402 },
                { pax: "32 - 35", quad: 1007, triple: 1144, double: 1419 },
                { pax: "28 - 31", quad: 1027, triple: 1165, double: 1442 },
                { pax: "24 - 27", quad: 1055, triple: 1193, double: 1471 },
                { pax: "20 - 23", quad: 1093, triple: 1233, double: 1513 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_breakfast", default: "Breakfast di hotel." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_welcome_drink", default: "Welcoming drink zamzam dan kurma saat kedatangan." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_fruit_parcel", default: "Parcel buah di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
                { key: "include_foc", default: "FOC 1 pax." },
            ],
            excludes: [
                { key: "exclude_lunch_dinner", default: "Makan siang dan malam." },
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
        {
            id: "b4_10n",
            nameKey: "laB2b_b4_10n_name",
            name: "Bintang 4",
            durationKey: "laB2b_10n_duration",
            duration: "Program 10 Malam",
            stars: 4,
            hotelMadinah: "Deyar Al Eiman (5 Malam)",
            hotelMakkah: "Azka Al Safa (5 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 798, triple: 900, double: 1105 },
                { pax: "40 - 43", quad: 808, triple: 910, double: 1115 },
                { pax: "36 - 39", quad: 820, triple: 923, double: 1128 },
                { pax: "32 - 35", quad: 835, triple: 938, double: 1144 },
                { pax: "28 - 31", quad: 855, triple: 958, double: 1165 },
                { pax: "24 - 27", quad: 881, triple: 985, double: 1193 },
                { pax: "20 - 23", quad: 918, triple: 1023, double: 1233 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_meals_3x", default: "Makan & minum 3x sehari." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_welcome_drink", default: "Welcoming drink zamzam dan kurma saat kedatangan." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_fruit_parcel", default: "Parcel buah di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
                { key: "include_foc", default: "FOC 1 pax." },
            ],
            excludes: [
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
        {
            id: "b3_10n",
            nameKey: "laB2b_b3_10n_name",
            name: "Bintang 3",
            durationKey: "laB2b_10n_duration",
            duration: "Program 10 Malam",
            stars: 3,
            hotelMadinah: "Durrat Al Eiman (5 Malam)",
            hotelMakkah: "Al Miqat Ajyad (5 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 662, triple: 741, double: 900 },
                { pax: "40 - 43", quad: 671, triple: 751, double: 910 },
                { pax: "36 - 39", quad: 683, triple: 763, double: 923 },
                { pax: "32 - 35", quad: 698, triple: 778, double: 938 },
                { pax: "28 - 31", quad: 717, triple: 797, double: 958 },
                { pax: "24 - 27", quad: 742, triple: 823, double: 985 },
                { pax: "20 - 23", quad: 778, triple: 860, double: 1023 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_meals_3x", default: "Makan & minum 3x sehari." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_welcome_drink", default: "Welcoming drink zamzam dan kurma saat kedatangan." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_fruit_parcel", default: "Parcel buah di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
                { key: "include_foc", default: "FOC 1 pax." },
            ],
            excludes: [
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        }
    ],
    program7Nights: [
        {
            id: "b5_fb_7n",
            nameKey: "laB2b_b5_fb_7n_name",
            name: "Bintang 5 (Fullboard)",
            durationKey: "laB2b_7n_duration",
            duration: "Program 7 Malam",
            stars: 5,
            hotelMadinah: "Pullman Zamzam (3 Malam)",
            hotelMakkah: "Pullman Zamzam (4 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 1007, triple: 1132, double: 1382 },
                { pax: "40 - 43", quad: 1017, triple: 1143, double: 1393 },
                { pax: "36 - 39", quad: 1030, triple: 1156, double: 1407 },
                { pax: "32 - 35", quad: 1046, triple: 1172, double: 1424 },
                { pax: "28 - 31", quad: 1067, triple: 1194, double: 1447 },
                { pax: "24 - 27", quad: 1095, triple: 1222, double: 1477 },
                { pax: "20 - 23", quad: 1135, triple: 1263, double: 1520 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_meals_3x", default: "Makan & minum 3x sehari." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_welcome_drink", default: "Welcoming drink zamzam dan kurma saat kedatangan." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
            ],
            excludes: [
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
        {
            id: "b5_bf_7n",
            nameKey: "laB2b_b5_bf_7n_name",
            name: "Bintang 5 (Breakfast)",
            durationKey: "laB2b_7n_duration",
            duration: "Program 7 Malam",
            stars: 5,
            hotelMadinah: "Pullman Zamzam (3 Malam)",
            hotelMakkah: "Pullman Zamzam (4 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 772, triple: 870, double: 1065 },
                { pax: "40 - 43", quad: 782, triple: 880, double: 1075 },
                { pax: "36 - 39", quad: 793, triple: 892, double: 1088 },
                { pax: "32 - 35", quad: 808, triple: 907, double: 1104 },
                { pax: "28 - 31", quad: 827, triple: 926, double: 1124 },
                { pax: "24 - 27", quad: 853, triple: 953, double: 1152 },
                { pax: "20 - 23", quad: 890, triple: 990, double: 1191 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_breakfast", default: "Breakfast di hotel." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_fruit_parcel", default: "Parcel buah di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
            ],
            excludes: [
                { key: "exclude_lunch_dinner", default: "Makan siang dan malam." },
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
        {
            id: "b4_7n",
            nameKey: "laB2b_b4_7n_name",
            name: "Bintang 4",
            durationKey: "laB2b_7n_duration",
            duration: "Program 7 Malam",
            stars: 4,
            hotelMadinah: "Deyar Al Eiman (3 Malam)",
            hotelMakkah: "Azka Al Safa (4 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 647, triple: 718, double: 862 },
                { pax: "40 - 43", quad: 656, triple: 728, double: 871 },
                { pax: "36 - 39", quad: 667, triple: 739, double: 883 },
                { pax: "32 - 35", quad: 681, triple: 753, double: 897 },
                { pax: "28 - 31", quad: 699, triple: 771, double: 916 },
                { pax: "24 - 27", quad: 723, triple: 796, double: 942 },
                { pax: "20 - 23", quad: 757, triple: 831, double: 978 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_meals_3x", default: "Makan & minum 3x sehari." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_welcome_drink", default: "Welcoming drink zamzam dan kurma saat kedatangan." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
            ],
            excludes: [
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
        {
            id: "b3_7n",
            nameKey: "laB2b_b3_7n_name",
            name: "Bintang 3",
            durationKey: "laB2b_7n_duration",
            duration: "Program 7 Malam",
            stars: 3,
            hotelMadinah: "Durrat Al Eiman (3 Malam)",
            hotelMakkah: "Al Miqat Ajyad (4 Malam)",
            pricing: [
                { pax: "44 - 47", quad: 545, triple: 600, double: 712 },
                { pax: "40 - 43", quad: 553, triple: 609, double: 721 },
                { pax: "36 - 39", quad: 564, triple: 620, double: 732 },
                { pax: "32 - 35", quad: 578, triple: 634, double: 746 },
                { pax: "28 - 31", quad: 595, triple: 652, double: 764 },
                { pax: "24 - 27", quad: 619, triple: 676, double: 789 },
                { pax: "20 - 23", quad: 652, triple: 709, double: 824 },
            ],
            includes: [
                { key: "include_visa", default: "Visa umrah & asuransi kesehatan Arab Saudi." },
                { key: "include_accommodation", default: "Akomodasi hotel Madinah & Makkah sesuai program." },
                { key: "include_meals_3x", default: "Makan & minum 3x sehari." },
                { key: "include_transport", default: "Transportasi selama di Arab Saudi." },
                { key: "include_ziarah", default: "Transportasi ziarah Makkah 1x dan Madinah 1x." },
                { key: "include_muthawwif", default: "Muthawwif atau pembimbing ibadah berbahasa Indonesia." },
                { key: "include_mutawwifah", default: "Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat." },
                { key: "include_snack_premium_4x", default: "Snack premium 4x trip." },
                { key: "include_handling_airport", default: "Handling kedatangan dan kepulangan di bandara." },
                { key: "include_meal_box", default: "Meal box kedatangan dan kepulangan di bandara." },
                { key: "include_handling_hotel", default: "Handling check-in dan check-out hotel." },
                { key: "include_porter_tips", default: "Tips porter bandara dan bellboy hotel." },
                { key: "include_luggage_dist", default: "Distribusi koper ke kamar jama'ah." },
                { key: "include_water", default: "Air mineral di setiap kamar jama'ah." },
                { key: "include_fruit_parcel", default: "Parcel buah di setiap kamar jama'ah." },
                { key: "include_greeting_card", default: "Greeting card di setiap kamar jama'ah." },
                { key: "include_zamzam", default: "Free air zamzam 5lt." },
            ],
            excludes: [
                { key: "exclude_flight_ticket", default: "Tiket pesawat." },
                { key: "exclude_travel_insurance", default: "Asuransi perjalanan." },
                { key: "exclude_vax_meningitis", default: "Vaksin meningitis." },
                { key: "exclude_personal_expenses", default: "Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program." },
                { key: "exclude_visa_surcharge", default: "Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah." },
            ],
        },
    ],
};

const LaB2bCard = ({ pkg, selectedCurrency, index }) => {
    const { translations, language } = useContext(LanguageContext);
    const { rates } = useCurrency();
    const getTranslation = (key, fallback) => translations[key] || fallback;

    const generateWhatsAppLink = (pkgName, duration) => {
        const fullPackageName = `${pkgName} (${duration})`;
        const contactName = getTranslation('arrahmahTeam', 'Tim Arrahmah');

        let greeting;
        if (language === 'ar') {
            greeting = `${getTranslation('greeting_ar', 'السلام عليكم')} ${contactName}`;
        } else if (language === 'en') {
            greeting = `${getTranslation('greeting_en', 'Hello')} ${contactName}`;
        } else {
            greeting = `${getTranslation('greeting_id', 'Assalamu\'alaikum')} ${contactName}`;
        }

        const messageText =
            language === 'ar' ? getTranslation('wa_b2b_ar', '،\n\nأنا مهتم بباقة LA B2B {packageName}. هل يمكنني الحصول على مزinfor information?\n\n(تم إرسال الرسالة من الموقع الإلكتروني)').replace('{packageName}', fullPackageName) :
            language === 'en' ? getTranslation('wa_b2b_en', ',\n\nI am interested in the LA B2B package {packageName}. Can I get more information?\n\n(message sent from website)').replace('{packageName}', fullPackageName) :
            getTranslation('wa_b2b_id', ',\n\nSaya tertarik dengan paket LA B2B {packageName}. Bisakah saya mendapatkan informasi lebih lanjut?\n\n(pesan dikirim dari website)').replace('{packageName}', fullPackageName);

        const message = encodeURIComponent(`${greeting}${messageText}`);
        return `https://wa.me/${WHATSAPP_NUMBER}?text=${message}`;
    };

    const containerVariants = {
        hidden: { opacity: 0, y: 50 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.5,
                delay: index * 0.1,
                type: 'spring',
                stiffness: 80
            }
        }
    };
    
    const translatedPackageName = getTranslation(pkg.nameKey, pkg.name);
    const translatedDuration = getTranslation(pkg.durationKey, pkg.duration);
    
    const startingPrice = pkg.pricing.length > 0 ? Math.min(...pkg.pricing.map(p => p.quad)) : 0;
    const convertedStartingPrice = convertCurrency(startingPrice, 'USD', selectedCurrency, rates);

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
        >
            <Card className="bg-linear-to-br from-gray-800/60 to-gray-900/80 border border-gray-700/60 rounded-3xl shadow-2xl overflow-hidden flex flex-col backdrop-blur-xs h-full group">
                <CardHeader className="p-6">
                    <div className="flex justify-between items-start mb-2">
                        <CardTitle className="text-2xl font-bold text-white tracking-tight pr-4">{translatedPackageName}</CardTitle>
                        <div className="shrink-0 flex items-center space-x-1 bg-gray-900/50 px-3 py-1.5 rounded-full border border-gray-700">
                            {[...Array(pkg.stars)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-amber-400 fill-current" />
                            ))}
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="p-6 pt-0 grow flex flex-col">
                    <div className="space-y-4 mb-6">
                        <div className="bg-black/30 p-4 rounded-xl flex items-center">
                            <MapPin size={20} className="mr-4 text-gray-400 shrink-0"/>
                            <div>
                                <p className="text-sm text-gray-400">{getTranslation('madinah', 'Madinah')}</p>
                                <p className="font-semibold text-white">{pkg.hotelMadinah}</p>
                            </div>
                        </div>
                        <div className="bg-black/30 p-4 rounded-xl flex items-center">
                            <MapPin size={20} className="mr-4 text-gray-400 shrink-0"/>
                            <div>
                                <p className="text-sm text-gray-400">{getTranslation('makkah', 'Makkah')}</p>
                                <p className="font-semibold text-white">{pkg.hotelMakkah}</p>
                            </div>
                        </div>
                    </div>

                    <div className="mt-auto pt-6">
                        <div className="text-center mb-6">
                            <p className="text-gray-400 text-sm mb-1">{getTranslation('startingFrom', 'Mulai dari')}</p>
                            <div className="text-4xl font-bold text-white tracking-tight">
                                <CurrencyDisplay amount={convertedStartingPrice} currency={selectedCurrency} lang={language} isLarge={true} />
                            </div>
                             <p className="text-gray-400 text-xs mt-1">/ {getTranslation('pax', 'PAX')}</p>
                        </div>
                        
                        <Accordion type="single" collapsible className="w-full">
                            <AccordionItem value="details" className="border-none">
                                <AccordionTrigger className="text-sm text-amber-400 hover:no-underline justify-center font-semibold">
                                     {getTranslation('viewDetails', 'Lihat Rincian')}
                                     <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
                                </AccordionTrigger>
                                <AccordionContent className="pt-6">
                                    <div className="space-y-6">
                                        <div className="bg-black/30 rounded-xl p-4">
                                            <div className="grid grid-cols-4 gap-x-2 text-xs text-gray-400 font-bold uppercase tracking-wider mb-3 text-center">
                                                <span className="text-left flex items-center"><Users size={12} className="mr-1.5"/>{getTranslation('pax', 'PAX')}</span>
                                                <span className="flex items-center justify-center"><BedDouble size={12} className="mr-1.5"/>{getTranslation('quad', 'QUAD')}</span>
                                                <span className="flex items-center justify-center"><BedDouble size={12} className="mr-1.5"/>{getTranslation('triple', 'TRIPLE')}</span>
                                                <span className="flex items-center justify-center"><BedDouble size={12} className="mr-1.5"/>{getTranslation('double', 'DOUBLE')}</span>
                                            </div>
                                            <div className="space-y-2">
                                                {pkg.pricing.map((tier, i) => (
                                                <div key={i} className="grid grid-cols-4 gap-x-2 items-center py-2 border-b border-white/10 last:border-b-0">
                                                    <div className="font-medium text-gray-300 flex items-center text-sm">{tier.pax}</div>
                                                    <CurrencyDisplay amount={convertCurrency(tier.quad, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                                                    <CurrencyDisplay amount={convertCurrency(tier.triple, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                                                    <CurrencyDisplay amount={convertCurrency(tier.double, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                                                </div>
                                                ))}
                                            </div>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-green-400 mb-3 flex items-center text-base"><CheckCircle2 size={18} className="mr-2"/> {getTranslation('includes', 'Termasuk')}</h4>
                                            <ul className="space-y-2">
                                            {pkg.includes.map((item, i) => (
                                                <li key={i} className="flex items-start text-sm">
                                                    <CheckCircle2 className="w-4 h-4 mr-2.5 mt-0.5 shrink-0 text-green-400/80" />
                                                    <span className="text-gray-300">{getTranslation(item.key, item.default)}</span>
                                                </li>
                                            ))}
                                            </ul>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-red-400 mb-3 flex items-center text-base"><XCircle size={18} className="mr-2"/> {getTranslation('excludes', 'Tidak Termasuk')}</h4>
                                            <ul className="space-y-2">
                                            {pkg.excludes.map((item, i) => (
                                                 <li key={i} className="flex items-start text-sm">
                                                    <XCircle className="w-4 h-4 mr-2.5 mt-0.5 shrink-0 text-red-400/80" />
                                                    <span className="text-gray-300">{getTranslation(item.key, item.default)}</span>
                                                </li>
                                            ))}
                                            </ul>
                                        </div>
                                    </div>
                                </AccordionContent>
                            </AccordionItem>
                        </Accordion>

                        <Button asChild className="w-full gold-gradient text-black font-bold text-base py-3 mt-6 hover:opacity-90 transition-opacity transform hover:scale-105 duration-300">
                            <a href={generateWhatsAppLink(translatedPackageName, translatedDuration)} target="_blank" rel="noopener noreferrer">
                                <Phone size={18} className="mr-2.5"/> {getTranslation('contactForBooking', 'Pesan Paket Ini')}
                            </a>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </motion.div>
    );
};


const LaB2bPackages = ({ selectedCurrency }) => {
    const { translations, language } = useContext(LanguageContext);
    const getTranslation = (key, fallback) => translations[key] || fallback;

    const generateWhatsAppCustomLink = () => {
        const contactName = getTranslation('arrahmahTeam', 'Tim Arrahmah');
        let greeting;
        if (language === 'ar') {
            greeting = `${getTranslation('greeting_ar', 'السلام عليكم')} ${contactName}`;
        } else if (language === 'en') {
            greeting = `${getTranslation('greeting_en', 'Hello')} ${contactName}`;
        } else {
            greeting = `${getTranslation('greeting_id', 'Assalamu\'alaikum')} ${contactName}`;
        }
        
        const messageText = 
            language === 'ar' ? getTranslation('wa_custom_b2b_ar', '،\n\nأرغب في طلب باقة LA B2B مخصصة. هل يمكنكم مساعدتي؟\n\n(تم إرسال الرسالة من الموقع الإلكتروني)') :
            language === 'en' ? getTranslation('wa_custom_b2b_en', ',\n\nI would like to request a custom LA B2B package. Can you assist me?\n\n(message sent from website)') :
            getTranslation('wa_custom_b2b_id', ',\n\nSaya ingin meminta paket LA B2B custom. Bisakah dibantu?\n\n(pesan dikirim dari website)');

        const message = encodeURIComponent(`${greeting}${messageText}`);
        return `https://wa.me/${WHATSAPP_NUMBER}?text=${message}`;
    };
    
    const PackageGrid = ({ packages }) => (
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {packages.sort((a, b) => b.stars - a.stars).map((pkg, index) => (
              <LaB2bCard key={pkg.id} pkg={pkg} selectedCurrency={selectedCurrency} index={index} />
          ))}
      </div>
    );

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
        >
            <div className="text-center md:text-left mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-white tracking-tight">
                    {getTranslation('laB2bPackagesTitle', 'Penawaran Paket LA B2B')}
                </h2>
                <p className="text-lg text-gray-400 max-w-3xl mt-2 leading-relaxed">
                    {getTranslation('laB2bPackagesSubtitle', 'Solusi Land Arrangement lengkap dan kompetitif untuk partner travel agent. Periode: 10 Jul - 30 Sep 2025.')}
                </p>
            </div>
            
            <Tabs defaultValue="10-nights" className="w-full mb-16">
              <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto h-12 p-1.5 bg-gray-800/80 rounded-xl border border-gray-700 mb-10">
                <TabsTrigger value="10-nights" className="tab-trigger-duration">
                    <Moon className="w-5 h-5 mr-2"/>
                    {getTranslation('program_10_nights', 'Program 10 Malam')}
                </TabsTrigger>
                <TabsTrigger value="7-nights" className="tab-trigger-duration">
                    <Moon className="w-5 h-5 mr-2"/>
                    {getTranslation('program_7_nights', 'Program 7 Malam')}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="10-nights">
                <PackageGrid packages={packagesData.program10Nights} />
              </TabsContent>
              <TabsContent value="7-nights">
                <PackageGrid packages={packagesData.program7Nights} />
              </TabsContent>
            </Tabs>

            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="text-center bg-gray-800/50 p-8 rounded-2xl shadow-xl border border-gray-700 relative overflow-hidden backdrop-blur-xs"
            >
                <div className="absolute -top-10 -right-10 w-40 h-40 bg-linear-to-br from-amber-500/10 to-transparent rounded-full blur-2xl"></div>
                <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-linear-to-tl from-amber-500/10 to-transparent rounded-full blur-2xl"></div>
                <Briefcase className="w-12 h-12 mx-auto mb-5 text-amber-400" />
                <h3 className="text-2xl font-bold text-white mb-3 tracking-tight">
                    {getTranslation('needCustomPackage', 'Butuh Paket Custom?')}
                </h3>
                <p className="text-gray-300 mb-6 max-w-xl mx-auto leading-relaxed">
                    {getTranslation('customPackageText', 'Jika ingin custom paket atau memiliki permintaan khusus untuk hotel, durasi, dan layanan lainnya, jangan ragu untuk menghubungi tim admin kami.')}
                </p>
                <Button asChild size="lg" className="gold-gradient text-black font-bold px-8 py-3.5 group text-base transform hover:scale-105 duration-300">
                    <a href={generateWhatsAppCustomLink()} target="_blank" rel="noopener noreferrer">
                       <MessageSquare className="mr-2.5 h-5 w-5 transition-transform duration-300 group-hover:rotate-[-10deg]" /> {getTranslation('contactAdmin', 'Hubungi Admin')}
                    </a>
                </Button>
            </motion.div>
        </motion.div>
    )
}

export default LaB2bPackages;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Check, X, Package, Star, ShoppingCart } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from '@/lib/utils';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useNavigate } from 'react-router-dom';

const PlanCardDisplay = ({ plan, index, type }) => {
  const { translations } = useContext(LanguageContext);
  const navigate = useNavigate();

  const getTranslation = (key, fallback) => translations[key] || fallback;

  const handleOrder = () => {
    let orderDetails = {
      packageType: type,
      category: plan.name,
    };
    navigate('/order', { state: { orderDetails } });
  };

  const itemsToRemove = [
    'Oprasional Mutawwif',
    'Visa Umrah',
    'Bus AC Premium 5 trip',
    'Asuransi Arab Saudi',
    '<PERSON><PERSON><PERSON><PERSON>'
  ];

  const filteredIncludes = plan.includes.filter(feature => {
    if (plan.name === 'Premium' || plan.name === 'VIP') {
      return !itemsToRemove.includes(feature.trim());
    }
    return true;
  });

  return (
    <motion.div
      key={plan.name}
      initial={{ opacity: 0, y: 60, scale: 0.9 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.6, delay: index * 0.1, type: 'spring', stiffness: 100 }}
      className={cn(
        'relative rounded-2xl overflow-hidden flex flex-col shadow-2xl transform hover:shadow-glow transition-all duration-400 ease-out group',
        plan.gradientClass,
        plan.popular ? 'border-4 border-amber-400' : 'border border-gray-700'
      )}
    >
      {plan.popular && (
        <div className="absolute top-0 right-0 bg-amber-400 text-black font-bold px-4 py-1.5 text-xs z-10 rounded-bl-lg shadow-md flex items-center">
          <Star className="w-3 h-3 mr-1.5 fill-current" /> {getTranslation('mostPopular', 'MOST POPULAR')}
        </div>
      )}
      
      <div className="p-8 text-center">
        <Package className={cn("w-12 h-12 mx-auto mb-4 opacity-80 group-hover:opacity-100 transition-opacity", plan.textColor)} />
        <h3 className={cn("text-3xl font-extrabold mb-3 tracking-tight", plan.textColor === 'text-slate-100' ? 'text-white' : plan.textColor)}>{plan.name}</h3>
        <p className={cn("mb-6 text-sm opacity-90", plan.textColor === 'text-slate-100' ? 'text-gray-300' : plan.textColor)}>{plan.description}</p>
        <Button 
          onClick={handleOrder}
          className={cn(
            'w-full text-base font-semibold py-3.5 transition-all duration-300 ease-in-out transform hover:scale-105 group-hover:shadow-xl',
            plan.popular ? plan.popularButtonClass : plan.buttonClass
          )}
        >
          <ShoppingCart className="mr-2 h-5 w-5" />
          {getTranslation('selectPackage', 'Pesan Paket')} {plan.name}
        </Button>
      </div>
      
      <div className="p-6 bg-black/30 backdrop-blur-xs grow border-t border-white/10">
        <Accordion type="single" collapsible className="w-full" defaultValue="includes">
          <AccordionItem value="includes" className="border-b-white/10">
            <AccordionTrigger className={cn("hover:no-underline font-semibold", plan.textColor === 'text-slate-100' ? 'text-white' : plan.textColor)}>{getTranslation('includes', 'Includes')}</AccordionTrigger>
            <AccordionContent>
              <ul className="space-y-2.5 pt-3">
                {filteredIncludes.map((feature, i) => (
                  <li key={i} className="flex items-start">
                    <Check className="h-5 w-5 text-green-400 mr-2.5 shrink-0 mt-0.5" />
                    <span className={cn("text-sm", plan.textColor === 'text-slate-100' ? 'text-gray-300' : `${plan.textColor} opacity-90`)}>{feature}</span>
                  </li>
                ))}
              </ul>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="excludes" className="border-b-white/10">
            <AccordionTrigger className={cn("hover:no-underline font-semibold", plan.textColor === 'text-slate-100' ? 'text-white' : plan.textColor)}>{getTranslation('excludes', 'Excludes')}</AccordionTrigger>
            <AccordionContent>
              <ul className="space-y-2.5 pt-3">
                {plan.excludes.map((feature, i) => (
                  <li key={i} className="flex items-start">
                    <X className="h-5 w-5 text-red-400 mr-2.5 shrink-0 mt-0.5" />
                    <span className={cn("text-sm", plan.textColor === 'text-slate-100' ? 'text-gray-300' : `${plan.textColor} opacity-90`)}>{feature}</span>
                  </li>
                ))}
              </ul>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="notes" className="border-b-0">
            <AccordionTrigger className={cn("hover:no-underline font-semibold", plan.textColor === 'text-slate-100' ? 'text-white' : plan.textColor)}>{getTranslation('notes', 'Notes')}</AccordionTrigger>
            <AccordionContent>
               <ul className="space-y-2.5 pt-3">
                {plan.notes.map((note, i) => (
                  <li key={i} className="flex items-start">
                    <span className={cn("text-sm ml-7", plan.textColor === 'text-slate-100' ? 'text-gray-300' : `${plan.textColor} opacity-90`)}>- {note}</span>
                  </li>
                ))}
              </ul>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </motion.div>
  );
};

export default PlanCardDisplay;
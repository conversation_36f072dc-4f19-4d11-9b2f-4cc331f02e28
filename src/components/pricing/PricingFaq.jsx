import React, { useContext } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion.jsx";
import { LanguageContext } from '@/contexts/LanguageContext';
import { ChevronDown } from 'lucide-react';

const PricingFaq = () => {
  const { translations } = useContext(LanguageContext);

  const faqs = [
    {
      questionKey: 'pricingFaqQ1',
      answerKey: 'pricingFaqA1',
      defaultQuestion: 'Apa perbedaan antara Paket Handling dan Paket Bundling?',
      defaultAnswer: 'Paket Handling hanya mencakup layanan di darat (handling) selama di Arab Saudi. Paket Bundling adalah solusi lengkap yang sudah termasuk Visa Umrah resmi, transportasi, asuransi, dan semua layanan handling. Paket Bundling lebih praktis untuk travel yang tidak ingin mengurus visa secara terpisah.'
    },
    {
      questionKey: 'pricingFaqQ6',
      answerKey: 'pricingFaqA6',
      defaultQuestion: 'Apa itu paket "Handling Airport Only"?',
      defaultAnswer: 'Paket "Handling Airport Only" adalah layanan khusus yang hanya mencakup proses di bandara Arab Saudi, baik saat kedatangan (arrival) maupun kepulangan (departure). Paket ini cocok untuk travel yang sudah memiliki tim sendiri untuk layanan di luar bandara (hotel, ziarah, dll) namun membutuhkan bantuan profesional untuk proses imigrasi, bagasi, dan koordinasi di bandara.'
    },
    {
      questionKey: 'pricingFaqQ2',
      answerKey: 'pricingFaqA2',
      defaultQuestion: 'Apakah harga yang tertera sudah final?',
      defaultAnswer: 'Harga yang tertera adalah harga per jamaah (pax) dan dapat berubah sewaktu-waktu menyesuaikan kurs mata uang (USD/SAR ke IDR) dan kebijakan dari pemerintah Arab Saudi. Namun, harga yang sudah disepakati dalam kontrak tidak akan berubah.'
    },
    {
      questionKey: 'pricingFaqQ3',
      answerKey: 'pricingFaqA3',
      defaultQuestion: 'Bagaimana jika jumlah jamaah saya kurang dari batas minimum?',
      defaultAnswer: 'Untuk grup dengan jumlah jamaah di bawah batas minimum yang tertera (misal, di bawah 8 pax untuk paket handling atau di bawah 35 pax untuk paket airport only), kami menyediakan harga khusus. Silakan hubungi tim penjualan kami melalui WhatsApp untuk konsultasi dan mendapatkan penawaran terbaik.'
    },
    {
      questionKey: 'pricingFaqQ4',
      answerKey: 'pricingFaqA4',
      defaultQuestion: 'Layanan apa yang tidak termasuk dalam semua paket?',
      defaultAnswer: 'Semua paket tidak termasuk layanan pendampingan khusus (Mutawwif/Mutawwifah) untuk ke Raudhah dan jasa pendorong kursi roda. Layanan ini tersedia sebagai tambahan (add-on) dengan biaya terpisah.'
    },
     {
      questionKey: 'pricingFaqQ5',
      answerKey: 'pricingFaqA5',
      defaultQuestion: 'Bagaimana sistem pembayaran paket-paket ini?',
      defaultAnswer: 'Pembayaran dilakukan melalui transfer bank ke rekening resmi perusahaan. Kami biasanya meminta uang muka (down payment) saat konfirmasi pemesanan, dan pelunasan dilakukan sebelum tanggal keberangkatan. Detail lengkap akan tertera di invoice.'
    },
  ];

  return (
    <div className="max-w-3xl mx-auto">
      <Accordion type="single" collapsible className="w-full space-y-4">
        {faqs.map((faq, index) => (
          <AccordionItem 
            key={index} 
            value={`item-${index}`} 
            className="bg-linear-to-br from-gray-800 to-gray-900 border border-gray-700/70 rounded-lg shadow-lg overflow-hidden"
          >
            <AccordionTrigger className="flex items-center justify-between w-full p-6 text-left text-lg font-medium text-white hover:no-underline hover:bg-white/5 transition-colors">
              <span className="flex-1 pr-4">{translations[faq.questionKey] || faq.defaultQuestion}</span>
              <ChevronDown className="h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200" />
            </AccordionTrigger>
            <AccordionContent className="p-6 pt-0 text-gray-300 text-base leading-relaxed">
              <p>{translations[faq.answerKey] || faq.defaultAnswer}</p>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default PricingFaq;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext';

const PricingHeader = ({ title, description }) => {
  const { translations } = useContext(LanguageContext);

  const headerTitle = title || translations.pricingPageTitle || 'Harga & Paket Fleksibel';
  const headerDescription = description || translations.pricingPageDescription || 'Temukan solusi handling dan bundling umrah yang sesuai kebutuhan perjalanan Anda. Kami menawarkan pilihan transparan dengan nilai terbaik.';

  return (
    <div className="relative overflow-hidden bg-linear-to-br from-gray-900 to-black py-24 sm:py-32">
      <div className="absolute inset-x-0 bottom-0 h-px bg-linear-to-r from-gray-800/0 via-gray-700/50 to-gray-800/0"></div>
      <div className="absolute inset-0 z-0 opacity-10">
        <img-replace alt="Geometric patterns symbolizing structure and growth" class="w-full h-full object-cover" />
      </div>
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8 z-10">
        <motion.div 
          className="mx-auto max-w-2xl text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h1 className="text-4xl font-extrabold tracking-tight text-white sm:text-6xl">
            {headerTitle}
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            {headerDescription}
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default PricingHeader;
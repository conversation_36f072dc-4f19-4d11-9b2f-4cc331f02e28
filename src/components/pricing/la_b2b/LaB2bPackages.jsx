import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { LanguageContext } from '@/contexts/LanguageContext';
import { MessageSquare, Briefcase, Moon } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { packagesData } from './packagesData';
import { generateWhatsAppCustomLink } from './whatsappHelper';
import LaB2bCard from './LaB2bCard';
import { useNavigate } from 'react-router-dom';

const LaB2bPackages = ({ selectedCurrency }) => {
    const { translations, language } = useContext(LanguageContext);
    const navigate = useNavigate();
    const getTranslation = (key, fallback) => translations[key] || fallback;

    const handleCustomOrder = () => {
      navigate('/order/custom-request');
    };
    
    const PackageGrid = ({ packages }) => (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {packages.sort((a, b) => b.stars - a.stars).map((pkg, index) => (
              <LaB2bCard key={pkg.id} pkg={pkg} selectedCurrency={selectedCurrency} index={index} />
          ))}
      </div>
    );

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
        >
            <div className="text-center md:text-left mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-white tracking-tight">
                    {getTranslation('laB2bPackagesTitle', 'Penawaran Paket LA B2B')}
                </h2>
                <p className="text-lg text-gray-400 max-w-3xl mt-2 leading-relaxed">
                    {getTranslation('laB2bPackagesSubtitle', 'Solusi Land Arrangement lengkap dan kompetitif untuk partner travel agent. Periode: 10 Jul - 30 Sep 2025.')}
                </p>
            </div>
            
            <Tabs defaultValue="10-nights" className="w-full mb-16">
              <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto h-12 p-1.5 bg-gray-800/80 rounded-xl border border-gray-700 mb-10">
                <TabsTrigger value="10-nights" className="tab-trigger-duration">
                    <Moon className="w-5 h-5 mr-2"/>
                    {getTranslation('program_10_nights', 'Program 10 Malam')}
                </TabsTrigger>
                <TabsTrigger value="7-nights" className="tab-trigger-duration">
                    <Moon className="w-5 h-5 mr-2"/>
                    {getTranslation('program_7_nights', 'Program 7 Malam')}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="10-nights">
                <PackageGrid packages={packagesData.program10Nights} />
              </TabsContent>
              <TabsContent value="7-nights">
                <PackageGrid packages={packagesData.program7Nights} />
              </TabsContent>
            </Tabs>

            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="text-center bg-gray-800/50 p-8 rounded-2xl shadow-xl border border-gray-700 relative overflow-hidden backdrop-blur-xs"
            >
                <div className="absolute -top-10 -right-10 w-40 h-40 bg-linear-to-br from-amber-500/10 to-transparent rounded-full blur-2xl"></div>
                <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-linear-to-tl from-amber-500/10 to-transparent rounded-full blur-2xl"></div>
                <Briefcase className="w-12 h-12 mx-auto mb-5 text-amber-400" />
                <h3 className="text-2xl font-bold text-white mb-3 tracking-tight">
                    {getTranslation('needCustomPackage', 'Butuh Paket Custom?')}
                </h3>
                <p className="text-gray-300 mb-6 max-w-xl mx-auto leading-relaxed">
                    {getTranslation('customPackageText', 'Jika ingin custom paket atau memiliki permintaan khusus untuk hotel, durasi, dan layanan lainnya, jangan ragu untuk menghubungi tim admin kami atau buat pesanan manual.')}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button onClick={handleCustomOrder} size="lg" className="gold-gradient text-black font-bold px-8 py-3.5 group text-base transform hover:scale-105 duration-300">
                       <MessageSquare className="mr-2.5 h-5 w-5 transition-transform duration-300 group-hover:rotate-[-10deg]" /> {getTranslation('buatPesananManual', 'Buat Pesanan Manual')}
                    </Button>
                    <Button asChild variant="outline" size="lg" className="border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-black font-bold px-8 py-3.5 group text-base transform hover:scale-105 duration-300">
                        <a href={generateWhatsAppCustomLink(language, getTranslation)} target="_blank" rel="noopener noreferrer">
                            {getTranslation('contactAdmin', 'Hubungi Admin')}
                        </a>
                    </Button>
                </div>
            </motion.div>
        </motion.div>
    )
}

export default LaB2bPackages;
import React, { useContext } from 'react';
import { Dialog, DialogPortal, DialogOverlay, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, ShoppingCart } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useNavigate } from 'react-router-dom';

const getEnhancedDescription = (title, translations) => {
  const serviceDescriptions = {
    'Layanan Handling B2B Umrah & Haji Khusus': `
      <h3>🤝 ${translations.b2b_heading1 || 'Kemitraan Strategis untuk Travel Agent'}</h3>
      <p>${translations.b2b_p1 || 'Kami menawarkan solusi lengkap untuk travel agent yang ingin memberikan layanan umrah dan haji terbaik kepada jamaah mereka, dengan sistem yang terintegrasi dan tim yang profesional di lapangan.'}</p>
      
      <h4>✨ ${translations.b2b_heading2 || 'Layanan yang Disediakan:'}</h4>
      <ul>
        <li>• <strong>${translations.b2b_li1 || 'Airport Handling:'}</strong> ${translations.b2b_li1_desc || 'Penjemputan dan pengantaran jamaah di bandara dengan tim profesional.'}</li>
        <li>• <strong>${translations.b2b_li2 || 'Hotel Management:'}</strong> ${translations.b2b_li2_desc || 'Booking dan koordinasi hotel bintang 3-5 dekat Haramain.'}</li>
        <li>• <strong>${translations.b2b_li3 || 'Transportation:'}</strong> ${translations.b2b_li3_desc || 'Armada bus AC modern untuk semua kebutuhan perjalanan.'}</li>
        <li>• <strong>${translations.b2b_li4 || 'Catering Service:'}</strong> ${translations.b2b_li4_desc || 'Makanan halal berkualitas dengan menu Indonesia dan internasional.'}</li>
        <li>• <strong>${translations.b2b_li5 || 'Mutawwif Guide:'}</strong> ${translations.b2b_li5_desc || 'Pemandu berpengalaman yang fasih berbahasa Indonesia.'}</li>
        <li>• <strong>${translations.b2b_li6 || 'Ziarah Program:'}</strong> ${translations.b2b_li6_desc || 'Kunjungan ke tempat-tempat bersejarah Islam.'}</li>
      </ul>
      
      <h4>🎯 ${translations.b2b_heading3 || 'Keunggulan Bermitra dengan Arrahmah:'}</h4>
      <ul>
        <li>• ${translations.b2b_li7 || 'Terdaftar resmi di Muassasah Arab Saudi.'}</li>
        <li>• ${translations.b2b_li8 || 'Tim lapangan berpengalaman lebih dari 8 tahun.'}</li>
        <li>• ${translations.b2b_li9 || 'Sistem monitoring real-time untuk jamaah.'}</li>
        <li>• ${translations.b2b_li10 || 'Support 24/7 selama di Tanah Suci.'}</li>
        <li>• ${translations.b2b_li11 || 'Harga kompetitif dengan kualitas premium.'}</li>
      </ul>
    `,
    'Haji Tanpa Antri (Haji Furoda)': `
        <h3>🕋 ${translations.hajj_heading1 || 'Program Haji Khusus Tanpa Menunggu'}</h3>
        <p>${translations.hajj_p1 || 'Solusi bagi Anda yang ingin segera menunaikan ibadah haji tanpa harus menunggu antrian bertahun-tahun, dengan visa haji resmi yang dikeluarkan oleh Kerajaan Arab Saudi.'}</p>
        
        <h4>🏨 ${translations.hajj_heading2 || 'Fasilitas Premium Termasuk:'}</h4>
        <ul>
            <li>• ${translations.hajj_li1 || 'Visa Haji Furoda Resmi.'}</li>
            <li>• ${translations.hajj_li2 || 'Hotel bintang 5 di Makkah & Madinah.'}</li>
            <li>• ${translations.hajj_li3 || 'Transportasi VIP selama di Arab Saudi.'}</li>
            <li>• ${translations.hajj_li4 || 'Tenda Maktab VIP di Arafah & Mina.'}</li>
            <li>• ${translations.hajj_li5 || 'Pemandu ibadah (Mutawwif) khusus.'}</li>
            <li>• ${translations.hajj_li6 || 'Manasik haji eksklusif sebelum keberangkatan.'}</li>
        </ul>
        
        <h4>📋 ${translations.hajj_heading3 || 'Alur & Proses:'}</h4>
        <p>${translations.hajj_p2 || 'Proses pendaftaran mudah, tim kami akan memandu Anda melalui setiap langkah, mulai dari pengumpulan dokumen hingga keberangkatan.'}</p>
    `,
    'Handling Bandara & Hotel': `
        <h3>✈️ ${translations.handling_heading1 || 'Layanan Handling Profesional & Mulus'}</h3>
        <p>${translations.handling_p1 || 'Tim berpengalaman kami siap membantu jamaah dari kedatangan hingga keberangkatan dengan pelayanan prima untuk memastikan pengalaman yang bebas dari rasa khawatir.'}</p>
        
        <h4>🛬 ${translations.handling_heading2 || 'Layanan Kedatangan:'}</h4>
        <ul>
            <li>• ${translations.handling_li1 || 'Penjemputan di gate bandara dengan papan nama travel agent.'}</li>
            <li>• ${translations.handling_li2 || 'Bantuan proses imigrasi dan bea cukai yang cepat.'}</li>
            <li>• ${translations.handling_li3 || 'Penanganan dan distribusi bagasi langsung ke bus.'}</li>
            <li>• ${translations.handling_li4 || 'Welcome drink & snack saat tiba di bus.'}</li>
        </ul>

        <h4>🏨 ${translations.handling_heading3 || 'Manajemen Hotel:'}</h4>
        <ul>
            <li>• ${translations.handling_li5 || 'Proses check-in express tanpa antri untuk jamaah.'}</li>
            <li>• ${translations.handling_li6 || 'Distribusi kunci dan koper langsung ke kamar masing-masing.'}</li>
            <li>• ${translations.handling_li7 || 'Tim standby 24/7 untuk segala kebutuhan jamaah di hotel.'}</li>
            <li>• ${translations.handling_li8 || 'Koordinasi check-out yang efisien.'}</li>
        </ul>
    `,
    'Pengurusan Visa Umrah & Haji': `
        <h3>📋 ${translations.visa_heading1 || 'Layanan Visa Cepat, Aman, dan Terpercaya'}</h3>
        <p>${translations.visa_p1 || 'Kami menyediakan layanan pengurusan visa umrah dan haji yang terintegrasi dengan sistem resmi pemerintah Arab Saudi, memastikan proses yang lancar dengan tingkat keberhasilan tinggi.'}</p>
        
        <h4>⚡ ${translations.visa_heading2 || 'Keunggulan Layanan Visa Kami:'}</h4>
        <ul>
            <li>• ${translations.visa_li1 || 'Terhubung langsung dengan sistem Nusuk & Muassasah.'}</li>
            <li>• ${translations.visa_li2 || 'Proses cepat (visa umrah 3-7 hari kerja).'}</li>
            <li>• ${translations.visa_li3 || 'Tingkat persetujuan visa 99%.'}</li>
            <li>• ${translations.visa_li4 || 'Konsultasi gratis mengenai persyaratan dokumen terbaru.'}</li>
            <li>• ${translations.visa_li5 || 'Asuransi perjalanan komprehensif termasuk dalam paket visa.'}</li>
        </ul>
    `,
    'Reservasi Akomodasi': `
        <h3>🏨 ${translations.acco_heading1 || 'Hotel Terbaik dengan Harga Kompetitif'}</h3>
        <p>${translations.acco_p1 || 'Kami memiliki kontrak langsung dengan berbagai pilihan hotel di Makkah dan Madinah, dari hotel ekonomis hingga mewah, semuanya dengan lokasi strategis dekat Masjidil Haram dan Masjid Nabawi.'}</p>
        
        <h4>🌟 ${translations.acco_heading2 || 'Kategori Hotel:'}</h4>
        <ul>
            <li>• <strong>${translations.acco_li1 || 'Bintang 3-4:'}</strong> ${translations.acco_li1_desc || 'Hotel ekonomis dan nyaman dengan fasilitas lengkap.'}</li>
            <li>• <strong>${translations.acco_li2 || 'Bintang 5:'}</strong> ${translations.acco_li2_desc || 'Hotel mewah dengan layanan premium dan lokasi sangat dekat.'}</li>
            <li>• <strong>${translations.acco_li3 || 'VIP/Pemandangan Ka\'bah:'}</strong> ${translations.acco_li3_desc || 'Akomodasi eksklusif dengan pemandangan langsung ke Ka\'bah atau pelataran Masjid Nabawi.'}</li>
        </ul>
    `,
    'Transportasi Jamaah': `
        <h3>🚌 ${translations.trans_heading1 || 'Armada Modern & Nyaman untuk Perjalanan Ibadah'}</h3>
        <p>${translations.trans_p1 || 'Kami menyediakan fleet transportasi terlengkap dengan standar keselamatan tertinggi untuk semua kebutuhan perjalanan jamaah selama di Arab Saudi.'}</p>
        
        <h4>🚐 ${translations.trans_heading2 || 'Jenis Kendaraan:'}</h4>
        <ul>
            <li>• <strong>${translations.trans_li1 || 'Bus Besar:'}</strong> ${translations.trans_li1_desc || 'Kapasitas 45-50 seat, cocok untuk grup besar.'}</li>
            <li>• <strong>${translations.trans_li2 || 'Mini Bus:'}</strong> ${translations.trans_li2_desc || 'Kapasitas 25-30 seat, ideal untuk grup sedang.'}</li>
            <li>• <strong>${translations.trans_li3 || 'Hiace/Coaster:'}</strong> ${translations.trans_li3_desc || 'Kapasitas 14-20 seat untuk grup kecil atau keluarga.'}</li>
            <li>• <strong>${translations.trans_li4 || 'Mobil VIP:'}</strong> ${translations.trans_li4_desc || '(GMC/Hyundai Staria) untuk layanan privat dan eksklusif.'}</li>
        </ul>
    `,
    'Katering Jamaah': `
        <h3>🍽️ ${translations.cat_heading1 || 'Kuliner Halal Berkualitas dengan Cita Rasa Nusantara'}</h3>
        <p>${translations.cat_p1 || 'Menyajikan hidangan lezat dan bergizi dengan menu Indonesia dan Asia yang halal dan thoyyib untuk memenuhi selera jamaah.'}</p>
        
        <h4>🥘 ${translations.cat_heading2 || 'Pilihan Menu & Layanan:'}</h4>
        <ul>
            <li>• ${translations.cat_li1 || 'Menu prasmanan (buffet) 3x sehari di hotel.'}</li>
            <li>• ${translations.cat_li2 || 'Pilihan menu khas Indonesia, Asia, dan internasional.'}</li>
            <li>• ${translations.cat_li3 || 'Lunch box praktis untuk perjalanan dan city tour.'}</li>
            <li>• ${translations.cat_li4 || 'Layanan katering dapat disesuaikan untuk diet khusus.'}</li>
        </ul>
    `,
    'Paket Tour & Ziarah Arab Saudi': `
        <h3>🕌 ${translations.tour_heading1 || 'Menelusuri Jejak Sejarah Islam'}</h3>
        <p>${translations.tour_p1 || 'Program ziarah lengkap ke tempat-tempat bersejarah di Makkah dan Madinah dengan pemandu (mutawwif) yang berpengalaman dan berpengetahuan luas.'}</p>
        
        <h4>📍 ${translations.tour_heading2 || 'Destinasi Ziarah Populer:'}</h4>
        <ul>
            <li>• <strong>${translations.tour_li1 || 'Makkah:'}</strong> ${translations.tour_li1_desc || 'Jabal Tsur, Jabal Nur, Jabal Rahmah, Arafah, Mina, dan Museum Haramain.'}</li>
            <li>• <strong>${translations.tour_li2 || 'Madinah:'}</strong> ${translations.tour_li2_desc || 'Masjid Quba, Masjid Qiblatain, Jabal Uhud, dan Kebun Kurma.'}</li>
            <li>• <strong>${translations.tour_li3 || 'Tour Tambahan:'}</strong> ${translations.tour_li3_desc || 'Kunjungan ke kota Thaif, museum, dan tempat-tempat modern di Arab Saudi (opsional).'}</li>
        </ul>
    `
  };
  return serviceDescriptions[title] || '';
};

const ServiceDetailModal = ({ isOpen, onClose, service }) => {
  const { translations } = useContext(LanguageContext);
  const navigate = useNavigate();

  const handleOrderService = () => {
    const orderPackage = {
      id: service.id,
      name: service.title,
      type: 'Service',
      serviceType: service.title
    };
    onClose();
    navigate('/order', { state: { orderPackage } });
  };
  
  const backdropVariants = {
    visible: { opacity: 1 },
    hidden: { opacity: 0 },
  };

  const modalVariants = {
    hidden: { y: "30px", opacity: 0, scale: 0.98 },
    visible: { 
      y: 0, 
      opacity: 1, 
      scale: 1,
      transition: { type: "spring", stiffness: 220, damping: 25 }
    },
    exit: { y: "30px", opacity: 0, scale: 0.98, transition: { duration: 0.2 } }
  };
  
  const enhancedDescription = getEnhancedDescription(service.title, translations);

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog open={isOpen} onOpenChange={onClose}>
          <DialogPortal>
            <DialogOverlay as={motion.div} variants={backdropVariants} initial="hidden" animate="visible" exit="hidden" />
            <DialogContent
              className="fixed z-50 grid w-full max-w-4xl gap-0 border bg-secondary p-0 shadow-lg sm:rounded-2xl md:w-full text-white top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              as={motion.div}
              variants={modalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              onEscapeKeyDown={onClose}
              onInteractOutside={onClose}
            >
              <DialogHeader className="p-6 pb-4">
                <DialogTitle className="text-2xl font-bold text-white leading-tight gradient-text mb-2">
                  {service.title}
                </DialogTitle>
              </DialogHeader>
              
              <ScrollArea className="max-h-[65vh] px-6">
                <div className="w-full h-64 rounded-lg overflow-hidden mb-6 shadow-lg border border-gray-700">
                  <img alt={service.imageAlt || service.title} className="w-full h-full object-cover" src={service.imageUrl} />
                </div>
                <DialogDescription as="div" className="text-gray-300 text-base leading-relaxed">
                  <div 
                    className="prose prose-sm prose-invert max-w-none prose-p:text-gray-300 prose-ul:list-disc prose-ul:pl-5 prose-li:my-1 prose-h3:text-amber-300 prose-h3:text-xl prose-h4:text-amber-400 prose-h4:text-lg prose-strong:text-amber-300"
                    dangerouslySetInnerHTML={{ __html: enhancedDescription || (translations.contentNotAvailable || "Detail layanan akan segera tersedia.") }} 
                  />
                </DialogDescription>
              </ScrollArea>

              <DialogFooter className="p-6 pt-4 mt-auto flex flex-col sm:flex-row justify-between gap-3 bg-secondary/50 rounded-b-2xl border-t border-gray-700">
                <Button 
                  onClick={onClose}
                  variant="outline" 
                  className="border-gray-500 text-gray-300 hover:bg-gray-700 hover:text-white"
                >
                  <X className="mr-2 h-4 w-4" /> {translations.close || "Tutup"}
                </Button>
                <Button 
                  onClick={handleOrderService}
                  className="gold-gradient text-black font-bold flex-1"
                >
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  {translations.orderService || "Pesan Layanan Ini"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </DialogPortal>
        </Dialog>
      )}
    </AnimatePresence>
  );
};

export default ServiceDetailModal;
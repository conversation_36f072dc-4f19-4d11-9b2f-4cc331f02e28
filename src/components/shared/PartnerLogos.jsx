import React, { useContext, useMemo } from 'react';
import { motion } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { useContent } from '@/contexts/ContentContext.jsx';
import { cn } from '@/lib/utils.js';

const PartnerLogos = ({ title }) => {
  const { partners: initialPartners, isLoading } = useContent();
  const { translations } = useContext(LanguageContext);

  const shuffledPartners = useMemo(() => {
    return initialPartners.sort(() => Math.random() - 0.5);
  }, [initialPartners]);
  
  const extendedLogos = useMemo(() => {
    return shuffledPartners.length > 0 ? [...shuffledPartners, ...shuffledPartners] : [];
  }, [shuffledPartners]);


  if (isLoading) {
    return (
      <section className="py-20 bg-background text-white">
        <div className="container mx-auto px-4 md:px-6 text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-700 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-700 rounded w-80 mx-auto mb-16"></div>
            <div className="h-32 bg-gray-800/70 rounded-2xl w-full"></div>
          </div>
        </div>
      </section>
    );
  }

  if (!initialPartners || initialPartners.length === 0) {
    return null;
  }
  
  const getTranslation = (key, fallback, variables = {}) => {
    let text = translations[key] || fallback;
    Object.keys(variables).forEach(varKey => {
      text = text.replace(`{${varKey}}`, variables[varKey]);
    });
    return text;
  }

  const marqueeVariants = {
    animate: {
      x: [0, -1 * (shuffledPartners.length * 264)], 
      transition: {
        x: {
          repeat: Infinity,
          repeatType: "loop",
          duration: shuffledPartners.length * 4,
          ease: "linear",
        },
      },
    },
  };
  
  return (
    <section className="py-20 bg-background text-white overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-3 text-white">
            {title || getTranslation('partnerLogosTitle', "Dipercaya oleh Ratusan Mitra Travel")}
          </h2>
          <p className="text-gray-400 mb-2 max-w-3xl mx-auto">
            {getTranslation(
              'partnerLogosSubtitle',
              "Kami bangga menjadi pilihan utama bagi lebih dari {count}+ biro perjalanan umrah di seluruh Indonesia, memberikan layanan handling yang andal dan profesional.",
              { count: initialPartners.length }
            )}
          </p>
          <div className="w-24 h-1.5 bg-linear-to-r from-amber-400 to-yellow-500 mx-auto rounded-full"></div>
        </motion.div>

        <div className="relative h-32 mask-[linear-gradient(to_right,transparent_0,black_128px,black_calc(100%-200px),transparent_100%)]">
           <motion.div
            className="absolute left-0 flex gap-x-8"
            variants={marqueeVariants}
            animate="animate"
          >
            {extendedLogos.map((logo, index) => (
              <div key={logo.id + '-' + index} className="shrink-0 w-60">
                <div className="bg-slate-100/90 hover:bg-white transition-all duration-300 p-5 rounded-2xl h-32 flex items-center justify-center shadow-lg">
                  <img
                    src={logo.logo}
                    alt={`${logo.name} logo`}
                    className="max-h-20 w-auto object-contain pointer-events-none"
                  />
                </div>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default PartnerLogos;
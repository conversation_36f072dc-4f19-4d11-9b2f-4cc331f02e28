import React, { useContext } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const SEO = ({ 
  title, 
  description, 
  keywords, 
  ogType = 'website',
  ogImage,
  ogTitle,
  ogDescription,
  twitterCard = 'summary_large_image',
  twitterTitle,
  twitterDescription,
  twitterImage,
  articlePublishedTime,
  articleAuthor,
  articleTags,
  schema,
  noIndex = false
}) => {
  const { language } = useContext(LanguageContext);
  const location = useLocation();
  
  const siteName = "Umrahservice.co";
  const defaultDescription = "Layanan handling umrah & land arrangement terbaik untuk travel agent & jamaah. UmrahService.co adalah mitra handling terpercaya Anda di Makkah & Madinah.";
  const fullTitle = `${title} | ${siteName}`;
  const finalDescription = description || defaultDescription;

  const baseUrl = "https://www.umrahservice.co";
  const defaultOgImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg";
  const image = ogImage || defaultOgImage;

  const canonicalUrl = `${baseUrl}${location.pathname}`;

  const imageWidth = 1200;
  const imageHeight = 630;

  return (
    <Helmet htmlAttributes={{ lang: language }}>
      <title>{fullTitle}</title>
      <meta name="description" content={finalDescription} />
      {keywords && <meta name="keywords" content={keywords} />}
      <link rel="canonical" href={canonicalUrl} />
      
      {noIndex && <meta name="robots" content="noindex, nofollow" />}

      {/* Open Graph Tags */}
      <meta property="og:title" content={ogTitle || title || siteName} />
      <meta property="og:description" content={ogDescription || finalDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:image:secure_url" content={image} />
      <meta property="og:image:type" content="image/jpeg" />
      <meta property="og:image:width" content={imageWidth.toString()} />
      <meta property="og:image:height" content={imageHeight.toString()} />
      <meta property="og:image:alt" content={ogTitle || title || 'Umrahservice.co Hero Image'} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={language === 'ar' ? 'ar_SA' : language === 'en' ? 'en_US' : 'id_ID'} />
      
      {ogType === 'article' && articlePublishedTime && <meta property="article:published_time" content={new Date(articlePublishedTime).toISOString()} />}
      {ogType === 'article' && articleAuthor && <meta property="article:author" content={articleAuthor} />}
      {ogType === 'article' && Array.isArray(articleTags) && articleTags.map(tag => (
         <meta property="article:tag" content={tag} key={tag} />
      ))}


      {/* Twitter Card Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content="@umrahserviceco" />
      <meta name="twitter:creator" content="@umrahserviceco" />
      <meta name="twitter:title" content={twitterTitle || ogTitle || title || siteName} />
      <meta name="twitter:description" content={twitterDescription || ogDescription || finalDescription} />
      <meta name="twitter:image" content={twitterImage || image} />
      <meta name="twitter:image:alt" content={twitterTitle || ogTitle || title || 'Umrahservice.co Hero Image'} />


      {schema && <script type="application/ld+json">{JSON.stringify(schema)}</script>}
    </Helmet>
  );
};

export default SEO;
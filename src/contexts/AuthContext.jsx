import React, { createContext, useState, useContext, useEffect } from 'react';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const storedUsers = JSON.parse(localStorage.getItem('adminUsers') || '[]');
      if (storedUsers.length === 0) {
        const defaultAdmin = {
          id: 'default-admin-1',
          name: 'Admin Utama',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Superadmin',
          lastLogin: 'Belum Pernah',
          status: 'Active',
          createdAt: new Date().toISOString()
        };
        localStorage.setItem('adminUsers', JSON.stringify([defaultAdmin]));
        console.log('Default admin account created by AuthProvider.');
      }

      const authStatus = localStorage.getItem('isAdminAuthenticated');
      const storedUser = localStorage.getItem('loggedInAdmin');
      
      if (authStatus === 'true' && storedUser) {
        setIsAuthenticated(true);
        setUser(JSON.parse(storedUser));
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (error) {
      console.error("Failed to initialize or parse auth data from localStorage", error);
      localStorage.removeItem('isAdminAuthenticated');
      localStorage.removeItem('loggedInAdmin');
      setIsAuthenticated(false);
      setUser(null);
    }
    setIsLoading(false);
  }, []);

  const login = (userData) => {
    localStorage.setItem('isAdminAuthenticated', 'true');
    localStorage.setItem('loggedInAdmin', JSON.stringify(userData));
    setIsAuthenticated(true);
    setUser(userData);

    try {
      const storedUsers = JSON.parse(localStorage.getItem('adminUsers') || '[]');
      const updatedUsers = storedUsers.map(u => {
        if (u.id === userData.id) {
          return { ...u, lastLogin: new Date().toLocaleString('id-ID', { dateStyle: 'medium', timeStyle: 'short' }) };
        }
        return u;
      });
      localStorage.setItem('adminUsers', JSON.stringify(updatedUsers));
    } catch (error) {
      console.error("Failed to update last login time:", error);
    }
  };

  const logout = () => {
    localStorage.removeItem('isAdminAuthenticated');
    localStorage.removeItem('loggedInAdmin');
    setIsAuthenticated(false);
    setUser(null);
  };

  const value = { isAuthenticated, user, isLoading, login, logout };

  return (
    <AuthContext.Provider value={value}>
      {!isLoading && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  return useContext(AuthContext);
};
import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast.js';
import { v4 as uuidv4 } from 'uuid';
import { blogStorageService } from '@/services/blogStorageService.js';
import { translateBlogPosts } from '@/utils/autoTranslate.js';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { allBlogPosts } from '@/data/blog/posts.js';


const BlogContext = createContext(null);

export const BlogProvider = ({ children }) => {
  const [originalPosts, setOriginalPosts] = useState([]);
  const [translatedPosts, setTranslatedPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { language, translations } = useContext(LanguageContext);
  const { toast } = useToast();

  const [blogSettings, setBlogSettings] = useState({
    postsPerPage: 6,
    showExcerpt: true,
    showAuthor: true,
    showDate: true,
    showCategory: true,
    enableComments: false,
    enableSearch: true,
    enableCategoryFilter: true,
    showViews: true,
    showSharingButtons: true
  });

  const loadPosts = useCallback(() => {
    setIsLoading(true);
    try {
      const storedPosts = blogStorageService.loadPosts();
      const combinedPosts = [...allBlogPosts];
      const storedPostIds = new Set(storedPosts?.map(p => p.id));
      
      allBlogPosts.forEach(defaultPost => {
          if(!storedPostIds.has(defaultPost.id)){
              storedPosts.push(defaultPost);
          }
      });
      
      const postsToLoad = (storedPosts && storedPosts.length > 0) ? storedPosts : allBlogPosts;
      setOriginalPosts(postsToLoad);
      if(!storedPosts || storedPosts.length === 0){
          blogStorageService.savePosts(allBlogPosts);
      }
      
    } catch (error) {
      console.error("Failed to load blog posts:", error);
      setOriginalPosts(allBlogPosts);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  const loadBlogSettings = useCallback(() => {
    try {
      const storedSettings = localStorage.getItem('blogSettings');
      if (storedSettings) {
        setBlogSettings(prev => ({ ...prev, ...JSON.parse(storedSettings) }));
      }
    } catch (error) {
      console.error("Failed to load blog settings:", error);
    }
  }, []);

  useEffect(() => {
    loadPosts();
    loadBlogSettings();
  }, [loadPosts, loadBlogSettings]);
  
  useEffect(() => {
    if (originalPosts.length > 0) {
        const translated = translateBlogPosts(originalPosts, language, translations);
        setTranslatedPosts(translated);
    } else {
        setTranslatedPosts([]);
    }
  }, [originalPosts, language, translations]);

  const updateLocalStorage = useCallback((updatedPosts) => {
    try {
      blogStorageService.savePosts(updatedPosts);
    } catch (error) {
      toast({
        title: "Gagal Menyimpan Data",
        description: "Tidak dapat menyimpan perubahan ke penyimpanan lokal.",
        variant: "destructive",
      });
    }
  }, [toast]);
  
  const updateBlogSettings = useCallback((newSettings) => {
    setBlogSettings(prev => {
      const updated = { ...prev, ...newSettings };
      try {
        localStorage.setItem('blogSettings', JSON.stringify(updated));
      } catch (error) {
        console.error("Failed to save blog settings:", error);
      }
      return updated;
    });
  }, []);

  const addPost = useCallback((newPostData) => {
    setOriginalPosts(prevPosts => {
        const postToAdd = {
          ...newPostData,
          id: uuidv4(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          views: 0,
        };
        const updatedPosts = [postToAdd, ...prevPosts];
        updateLocalStorage(updatedPosts);
        return updatedPosts;
    });
  }, [updateLocalStorage]);

  const updatePost = useCallback((updatedPostData) => {
    setOriginalPosts(prevPosts => {
        const updatedPosts = prevPosts.map(p => 
          p.id === updatedPostData.id 
            ? { ...p, ...updatedPostData, updatedAt: new Date().toISOString() } 
            : p
        );
        updateLocalStorage(updatedPosts);
        return updatedPosts;
    });
  }, [updateLocalStorage]);

  const deletePost = useCallback((postId) => {
    setOriginalPosts(prevPosts => {
        const updatedPosts = prevPosts.filter(p => p.id !== postId);
        updateLocalStorage(updatedPosts);
        return updatedPosts;
    });
  }, [updateLocalStorage]);

  const getPostById = useCallback((id) => {
    return originalPosts.find(p => p.id === id);
  }, [originalPosts]);
  
  const getPostBySlug = useCallback((slug) => {
    return translatedPosts.find(p => p.slug === slug);
  }, [translatedPosts]);

  const getLatestPosts = useCallback((limit = 5, excludeId = null) => {
    return translatedPosts
      .filter(p => p.status === 'Published' && new Date(p.publish_date) <= new Date() && p.id !== excludeId)
      .sort((a, b) => new Date(b.publish_date) - new Date(a.publish_date))
      .slice(0, limit);
  }, [translatedPosts]);

  const incrementViewCount = useCallback((postId) => {
    setOriginalPosts(prevPosts => {
      const alreadyViewed = sessionStorage.getItem(`viewed_${postId}`);
      if(alreadyViewed) {
        return prevPosts;
      }

      const updatedPosts = prevPosts.map(p => 
        p.id === postId 
            ? { ...p, views: (p.views || 0) + 1 } 
            : p
      );
      
      if(updatedPosts.some(p => p.id === postId && p.views > (prevPosts.find(op => op.id === postId)?.views || 0))) {
        updateLocalStorage(updatedPosts);
        sessionStorage.setItem(`viewed_${postId}`, 'true');
      }

      return updatedPosts;
    });
  }, [updateLocalStorage]);


  const value = { 
    posts: translatedPosts, 
    originalPosts,
    isLoading, 
    blogSettings,
    addPost, 
    updatePost, 
    deletePost, 
    getPostById, 
    getPostBySlug, 
    getLatestPosts,
    updateBlogSettings,
    incrementViewCount,
    refreshPosts: loadPosts 
  };

  return (
    <BlogContext.Provider value={value}>
      {children}
    </BlogContext.Provider>
  );
};

export const useBlog = () => {
  const context = useContext(BlogContext);
  if (context === null) {
    throw new Error('useBlog must be used within a BlogProvider');
  }
  return context;
};
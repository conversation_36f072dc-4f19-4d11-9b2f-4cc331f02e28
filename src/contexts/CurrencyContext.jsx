import React, { createContext, useState, useEffect, useContext } from 'react';

export const CurrencyContext = createContext();

export const useCurrency = () => useContext(CurrencyContext);

const staticRates = {
  USD: 1,
  IDR: 16250,
  SAR: 3.75,
};

const lastUpdated = new Date('2025-07-25T10:00:00Z').toISOString();

export const CurrencyProvider = ({ children }) => {
  const [rates, setRates] = useState(staticRates);
  const [lastRateUpdate, setLastRateUpdate] = useState(lastUpdated);

  const value = {
    rates,
    lastRateUpdate,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};
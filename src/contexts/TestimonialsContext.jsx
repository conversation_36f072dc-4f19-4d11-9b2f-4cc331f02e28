import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast.js';
import { v4 as uuidv4 } from 'uuid';

const TestimonialsContext = createContext(null);

const toBase64 = file => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
});

const defaultTestimonials = [
    {
        id: 'test-1',
        name: '<PERSON>',
        role: 'Pimpinan Al-Hidayah Travel',
        quote: 'Layanan handling dari A<PERSON>hmah sangat profesional. Tim mereka di lapangan sigap dan sangat membantu jamaah kami. Komunikasi juga lancar. Sangat direkomendasikan!',
        avatar: `https://i.pravatar.cc/150?u=ahmad<PERSON><PERSON><PERSON>`,
        stars: 5,
    },
    {
        id: 'test-2',
        name: '<PERSON><PERSON>',
        role: '<PERSON><PERSON><PERSON>, Desember 2024',
        quote: '<PERSON><PERSON><PERSON><PERSON><PERSON>, perjalanan umrah kami lancar berkat bantuan tim handling. Dari bandara sampai hotel semua diurus dengan baik. Terima kasih banyak.',
        avatar: `https://i.pravatar.cc/150?u=sitiaminah`,
        stars: 5,
    },
    {
        id: 'test-3',
        name: 'Budi Santoso',
        role: 'Direktur Barokah Wisata',
        quote: 'Kami sudah beberapa kali bekerja sama dan selalu puas. Respon cepat, solusi tepat, dan harga kompetitif. Arrahmah adalah mitra andalan kami di Saudi.',
        avatar: `https://i.pravatar.cc/150?u=budisantoso`,
        stars: 4,
    }
];

const loadFromLocalStorage = (key, defaultValue) => {
  try {
    const storedValue = localStorage.getItem(key);
    if (storedValue) {
      return JSON.parse(storedValue);
    }
    localStorage.setItem(key, JSON.stringify(defaultValue));
    return defaultValue;
  } catch (error) {
    console.error(`Error loading ${key} from localStorage:`, error);
    return defaultValue;
  }
};

const saveToLocalStorage = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error);
  }
};

export const TestimonialsProvider = ({ children }) => {
    const { toast } = useToast();
    const [testimonials, setTestimonials] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        setIsLoading(true);
        setTestimonials(loadFromLocalStorage('managedTestimonials', defaultTestimonials));
        setIsLoading(false);
    }, []);

    const updateTestimonialsState = useCallback((newTestimonials) => {
        setTestimonials(newTestimonials);
        saveToLocalStorage('managedTestimonials', newTestimonials);
    }, []);

    const addTestimonial = useCallback(async (testimonialData) => {
        let avatarUrl = testimonialData.avatar || `https://i.pravatar.cc/150?u=${uuidv4()}`;
        if (testimonialData.avatarFile) {
            try {
                avatarUrl = await toBase64(testimonialData.avatarFile);
            } catch (error) {
                console.error("Error converting avatar to base64:", error);
                toast({ title: 'Gagal Upload Avatar', variant: 'destructive' });
            }
        }
        
        const newTestimonial = { ...testimonialData, id: uuidv4(), avatar: avatarUrl };
        delete newTestimonial.avatarFile; 

        const updatedTestimonials = [newTestimonial, ...testimonials];
        updateTestimonialsState(updatedTestimonials);
        toast({
            title: 'Testimoni Ditambahkan',
            description: 'Testimoni baru telah berhasil ditambahkan.',
        });
    }, [testimonials, updateTestimonialsState, toast]);

    const editTestimonial = useCallback(async (updatedTestimonialData) => {
        let avatarUrl = updatedTestimonialData.avatar;
        if (updatedTestimonialData.avatarFile) {
             try {
                avatarUrl = await toBase64(updatedTestimonialData.avatarFile);
            } catch (error) {
                console.error("Error converting avatar to base64:", error);
                toast({ title: 'Gagal Upload Avatar', variant: 'destructive' });
            }
        }

        const finalData = { ...updatedTestimonialData, avatar: avatarUrl };
        delete finalData.avatarFile;

        const updatedTestimonials = testimonials.map(t => t.id === finalData.id ? finalData : t);
        updateTestimonialsState(updatedTestimonials);
        toast({
            title: 'Testimoni Diperbarui',
            description: 'Testimoni telah berhasil diperbarui.',
        });
    }, [testimonials, updateTestimonialsState, toast]);

    const deleteTestimonial = useCallback((testimonialId) => {
        const updatedTestimonials = testimonials.filter(t => t.id !== testimonialId);
        updateTestimonialsState(updatedTestimonials);
        toast({
            title: 'Testimoni Dihapus',
            description: 'Testimoni telah berhasil dihapus.',
            variant: 'destructive'
        });
    }, [testimonials, updateTestimonialsState, toast]);

    const value = {
        testimonials,
        isLoading,
        addTestimonial,
        editTestimonial,
        deleteTestimonial,
    };

    return (
        <TestimonialsContext.Provider value={value}>
            {children}
        </TestimonialsContext.Provider>
    );
};

export const useTestimonials = () => {
    const context = useContext(TestimonialsContext);
    if (!context) {
        throw new Error('useTestimonials must be used within a TestimonialsProvider');
    }
    return context;
};
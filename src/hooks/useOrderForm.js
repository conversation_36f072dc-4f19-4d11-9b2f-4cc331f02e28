import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { packagesData } from '@/components/pricing/la_b2b/packagesData';

const initialFormData = {
    bookerInfo: { travelName: '', picName: '', whatsapp: '', email: '', jamaahOrigin: '' },
    packageType: 'la_b2b',
    selectedLaB2bPackage: null,
    roomComposition: { quad: '', triple: '', double: '', single: '' },
    totalPax: 0,
    packagePricing: {
        category: 'basic',
        airportService: 'JED/MED – Kedatangan Saja',
        alBaikArrival: false,
        alBaikDeparture: false,
    },
    optionalAddons: {
        culinary: { al_rumansiah: false },
        thaif_tour: {
            bus_charter: false,
            cable_car: false,
            tobogan: false,
            lunch: false,
        },
        haramain_train: {
            makkah_madinah: false,
            madinah_makkah: false,
            madinah_jed: false,
            jed_madinah: false,
        },
    },
    mutawwifRequest: {
        needed: false,
    },
    flightDetails: {
        departureDate: null,
        departureAirline: '',
        departureFlightNumber: '',
        returnDate: null,
        returnAirline: '',
        returnFlightNumber: '',
        ticketStatus: 'Belum Ada',
    },
    hotelDetails: {
        hotels: [{ id: uuidv4(), hotelName: '', city: 'Makkah', checkIn: null, nights: '', checkOut: null }]
    },
    visaStatus: 'Belum punya visa',
    documents: {
        roomlist: null,
        ticket: null,
        visa: null,
        manifest: null,
    },
    notes: '',
};

export const useOrderForm = () => {
    const [formData, setFormData] = useState(initialFormData);
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const { state } = location;
        if (state) {
            if (state.selectedPackageId) {
                const allPackages = [...packagesData.program10Nights, ...packagesData.program7Nights];
                const pkg = allPackages.find(p => p.id === state.selectedPackageId);
                if (pkg) {
                    setFormData(prev => ({
                        ...prev,
                        packageType: 'la_b2b',
                        selectedLaB2bPackage: pkg,
                    }));
                }
            } else if (state.orderDetails) {
                const { packageType, category, airportService } = state.orderDetails;
                setFormData(prev => {
                    const newPackagePricing = { ...prev.packagePricing };
                    if (category) {
                        newPackagePricing.category = category.toLowerCase();
                    }
                    if (airportService) {
                        newPackagePricing.airportService = airportService;
                    }

                    return {
                        ...prev,
                        packageType: packageType,
                        packagePricing: newPackagePricing,
                    };
                });
            } else if (state.openCustomLa) {
                navigate('/order/custom-request', { state: { formData: formData }});
            }
        }
        window.history.replaceState({}, document.title);
    }, [location, navigate, formData]);

    const handleInputChange = useCallback((section, field, value) => {
        setFormData(prev => {
            if (field) {
                return { ...prev, [section]: { ...prev[section], [field]: value } };
            }
            return { ...prev, [section]: value };
        });
    }, []);

    const onPackageTypeChange = (value) => {
        if (value === 'custom_request') {
             navigate('/order/custom-request', { state: { formData: formData }});
        } else {
            handleInputChange('packageType', null, value);
            if (value !== 'la_b2b') {
                handleInputChange('selectedLaB2bPackage', null, null);
            }
        }
    };

    const onSelectLaB2bPackage = (pkg) => {
        handleInputChange('selectedLaB2bPackage', null, pkg);
    };

    useEffect(() => {
        const { quad, triple, double, single } = formData.roomComposition;
        const total = (parseInt(quad, 10) || 0) * 4 +
                      (parseInt(triple, 10) || 0) * 3 +
                      (parseInt(double, 10) || 0) * 2 +
                      (parseInt(single, 10) || 0) * 1;
        setFormData(prev => ({ ...prev, totalPax: total }));
    }, [formData.roomComposition]);

    return {
        formData,
        setFormData,
        handleInputChange,
        onPackageTypeChange,
        onSelectLaB2bPackage,
    };
};
import { useState, useEffect, useContext } from 'react';
import { pricingStructure, getPrice } from '@/data/pricingStructure';
import { LanguageContext } from '@/contexts/LanguageContext';

const packageTypeLabels = {
    handling_service: 'Paket Handling Service',
    bundling_visa_handling: 'Paket Bundling Visa + Handling',
    la_b2b: 'Paket LA B2B',
    handling_airport_only: 'Paket Handling Airport Only',
    custom_request: 'Permintaan Custom',
};

const MAX_PAX_PER_BUS = 47;

const distributePaxEvenly = (totalPax, numBuses) => {
    if (numBuses === 0) return [];
    const basePax = Math.floor(totalPax / numBuses);
    const remainder = totalPax % numBuses;
    const distribution = [];
    for (let i = 0; i < numBuses; i++) {
        distribution.push(basePax + (i < remainder ? 1 : 0));
    }
    return distribution;
};

export const useOrderSummary = (formData) => {
    const [summary, setSummary] = useState({
        packageName: '',
        totalPax: 0,
        pricePerPax: 0,
        subtotal: 0,
        addons: [],
        total: 0,
        groups: 0,
        groupDetails: [],
    });
    const { translations } = useContext(LanguageContext);
    const getTranslation = (key, fallback) => translations[key] || fallback;

    useEffect(() => {
        const { packageType, totalPax, packagePricing, optionalAddons, selectedLaB2bPackage, roomComposition, mutawwifRequest, hotelDetails } = formData;
        
        if (totalPax === 0) {
            setSummary({ packageName: '', totalPax: 0, pricePerPax: 0, subtotal: 0, addons: [], total: 0, groups: 0, groupDetails: [] });
            return;
        }

        const numBuses = Math.ceil(totalPax / MAX_PAX_PER_BUS);
        const paxDistribution = distributePaxEvenly(totalPax, numBuses);

        let subtotal = 0;
        let groupDetails = [];
        let packageName = packageTypeLabels[packageType] || '';

        if (packageType === 'handling_service' || packageType === 'bundling_visa_handling') {
            packageName = `${packageTypeLabels[packageType]} - ${packagePricing.category.charAt(0).toUpperCase() + packagePricing.category.slice(1)}`;
            groupDetails = paxDistribution.map((pax, index) => {
                const pricePerPax = getPrice(packageType, packagePricing.category, pax);
                const totalGroupPrice = pricePerPax * pax;
                subtotal += totalGroupPrice;
                return { 
                    bus: index + 1, 
                    pax: pax, 
                    package: packagePricing.category, 
                    price_per_pax: pricePerPax,
                    total_group_price: totalGroupPrice
                };
            });
        } else if (packageType === 'handling_airport_only') {
            packageName = `${packageTypeLabels[packageType]}`;
            const serviceKey = packagePricing.airportService;
            const basePrice = pricingStructure.handling_airport_only.prices[serviceKey] || 0;
            
            let alBaikCost = 0;
            if (packagePricing.alBaikArrival && !serviceKey.includes('Kepulangan Saja')) {
                alBaikCost += pricingStructure.handling_airport_only.alBaik;
            }
            if (packagePricing.alBaikDeparture && !serviceKey.includes('Kedatangan Saja')) {
                alBaikCost += pricingStructure.handling_airport_only.alBaik;
            }

            const pricePerPaxWithAlBaik = basePrice + alBaikCost;
            subtotal = pricePerPaxWithAlBaik * totalPax;
            
            groupDetails = paxDistribution.map((pax, index) => {
                 const totalGroupPrice = pricePerPaxWithAlBaik * pax;
                 return { 
                    bus: index + 1, 
                    pax: pax, 
                    price_per_pax: pricePerPaxWithAlBaik,
                    total_group_price: totalGroupPrice
                 }
            });

        } else if (packageType === 'la_b2b' && selectedLaB2bPackage) {
            packageName = getTranslation(selectedLaB2bPackage.nameKey, selectedLaB2bPackage.name);
            const { quad, triple, double, single } = roomComposition;
            const numQuad = parseInt(quad, 10) || 0;
            const numTriple = parseInt(triple, 10) || 0;
            const numDouble = parseInt(double, 10) || 0;
            const numSingle = parseInt(single, 10) || 0;
            
            groupDetails = paxDistribution.map((pax, index) => {
                 const pricingTier = selectedLaB2bPackage.pricing.find(p => {
                    const paxRange = p.pax.split(' - ').map(Number);
                    return pax >= paxRange[0] && pax <= (paxRange[1] || Infinity);
                });
                
                let pricePerPax = 0;
                let totalGroupPrice = 0;

                if (pricingTier) {
                    const mainPricingTier = selectedLaB2bPackage.pricing.find(p => {
                        const paxRange = p.pax.split(' - ').map(Number);
                        return totalPax >= paxRange[0] && totalPax <= (paxRange[1] || Infinity);
                    });

                    let totalPackageCost = 0;
                    if (mainPricingTier) {
                        totalPackageCost = (numQuad * mainPricingTier.quad * 4) + 
                                           (numTriple * mainPricingTier.triple * 3) + 
                                           (numDouble * mainPricingTier.double * 2) +
                                           (numSingle * (mainPricingTier.single || mainPricingTier.double) * 1);
                    }
                    
                    const avgPricePerPax = totalPax > 0 ? totalPackageCost / totalPax : 0;
                    pricePerPax = avgPricePerPax;
                    totalGroupPrice = pricePerPax * pax;
                    subtotal += totalGroupPrice;
                }

                return {
                    bus: index + 1,
                    pax: pax,
                    price_per_pax: pricePerPax,
                    total_group_price: totalGroupPrice
                };
            });
        }

        const addonsCost = [];
        const addonsPrices = pricingStructure.optional_addons;
        const { thaif_tour, haramain_train, culinary } = optionalAddons;

        if (mutawwifRequest.needed && (packageType === 'handling_service' || packageType === 'bundling_visa_handling')) {
            const totalNights = hotelDetails.hotels.reduce((acc, hotel) => acc + (parseInt(hotel.nights, 10) || 0), 0);
            const mutawwifDuration = totalNights > 0 ? totalNights + 1 : 0;

            if (mutawwifDuration > 0 && totalPax > 0) {
                const { cost_per_day_sr, sr_to_idr_rate, sr_to_usd_rate } = addonsPrices.mutawwif;
                const costPerPax = (cost_per_day_sr * sr_to_idr_rate * mutawwifDuration) / totalPax * sr_to_usd_rate;
                const totalMutawwifCost = costPerPax * totalPax;
                addonsCost.push({ name: `Mutawwif (${mutawwifDuration} hari)`, cost: totalMutawwifCost, perPax: costPerPax });
            }
        }

        if (culinary.al_rumansiah) addonsCost.push({ name: 'Makan di Al Rumansiah', cost: addonsPrices.culinary.al_rumansiah * totalPax });

        const thaifBuses = thaif_tour.bus_charter ? numBuses : 0;
        if (thaif_tour.bus_charter) addonsCost.push({ name: `Tour Thaif: Bus Charter (${thaifBuses} unit)`, cost: addonsPrices.thaif_tour.bus_charter * thaifBuses });
        if (thaif_tour.cable_car) addonsCost.push({ name: 'Tour Thaif: Cable Car', cost: addonsPrices.thaif_tour.cable_car * totalPax });
        if (thaif_tour.tobogan) addonsCost.push({ name: 'Tour Thaif: Tobogan', cost: addonsPrices.thaif_tour.tobogan * totalPax });
        if (thaif_tour.lunch) addonsCost.push({ name: 'Tour Thaif: Makan Siang', cost: addonsPrices.thaif_tour.lunch * totalPax });

        if (haramain_train.makkah_madinah) addonsCost.push({ name: 'Kereta: Makkah ➝ Madinah', cost: addonsPrices.haramain_train.makkah_madinah * totalPax });
        if (haramain_train.madinah_makkah) addonsCost.push({ name: 'Kereta: Madinah ➝ Makkah', cost: addonsPrices.haramain_train.madinah_makkah * totalPax });
        if (haramain_train.madinah_jed) addonsCost.push({ name: 'Kereta: Madinah ➝ JED', cost: addonsPrices.haramain_train.madinah_jed * totalPax });
        if (haramain_train.jed_madinah) addonsCost.push({ name: 'Kereta: JED ➝ Madinah', cost: addonsPrices.haramain_train.jed_madinah * totalPax });
        
        const totalAddonsCost = addonsCost.reduce((acc, addon) => acc + addon.cost, 0);
        const total = subtotal + totalAddonsCost;
        const pricePerPax = totalPax > 0 ? total / totalPax : 0;

        setSummary({ packageName, totalPax, pricePerPax, subtotal, addons: addonsCost, total, groups: numBuses, groupDetails });

    }, [formData, getTranslation]);

    return { summary };
};
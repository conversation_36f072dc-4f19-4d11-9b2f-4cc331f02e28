import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import idTranslations from '@/locales/id.json';
import enTranslations from '@/locales/en.json';
import arTranslations from '@/locales/ar.json';

i18n
  .use(LanguageDetector) // Detect user language
  .use(initReactI18next) // Passes i18n down to react-i18next
  .init({
    resources: {
      en: { translation: enTranslations },
      id: { translation: idTranslations },
      ar: { translation: arTranslations },
    },
    fallbackLng: 'id', // Use Indonesian if detected language is not available
    interpolation: {
      escapeValue: false, // React already safes from xss
    },
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'appLanguage', // Key to find language in localStorage
    },
  });

export default i18n;
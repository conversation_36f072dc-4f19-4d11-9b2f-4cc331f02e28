import { format } from 'date-fns';
import { id } from 'date-fns/locale';

const WHATSAPP_NUMBER = '6281289552018'; 

const formatLine = (label, value) => (value || value === 0) ? `*${label}:* ${value}\n` : '';
const formatHeader = (title) => `\n*${title.toUpperCase()}*\n--------------------------\n`;
const formatCurrency = (value) => `$${(value || 0).toLocaleString('en-US')}`;

export const generateOrderWhatsAppLink = (formData, summary) => {
    let message = `*PESANAN BARU - ARRAHMAH HANDLING SERVICE*\n\n`;
    message += `Halo <PERSON>, saya ingin melakukan pemesanan dengan detail sebagai berikut:\n`;

    // Booker Info
    message += formatHeader('1. Informasi Pemesan');
    message += formatLine('Nama Travel/Agen', formData.bookerInfo.travelName);
    message += formatLine('Nama PIC', formData.bookerInfo.picName);
    message += formatLine('No. WhatsApp', formData.bookerInfo.whatsapp);
    message += formatLine('Email', formData.bookerInfo.email);
    message += formatLine('Asal Jamaah', formData.bookerInfo.jamaahOrigin);

    // Package Type
    message += formatHeader('2. Jenis Paket & Komposisi');
    message += formatLine('Jenis Paket', summary.packageName);
    message += formatLine('Total Jamaah', `${formData.totalPax} orang`);
    if (formData.roomComposition.quad > 0) message += formatLine(' - Quad', formData.roomComposition.quad);
    if (formData.roomComposition.triple > 0) message += formatLine(' - Triple', formData.roomComposition.triple);
    if (formData.roomComposition.double > 0) message += formatLine(' - Double', formData.roomComposition.double);
    if (formData.roomComposition.single > 0) message += formatLine(' - Single', formData.roomComposition.single);

    // Pricing Details
    if (formData.packageType === 'handling_service' || formData.packageType === 'bundling_visa_handling') {
        message += formatLine('Kategori', formData.packagePricing.category);
    }
    if (formData.packageType === 'handling_airport_only') {
        message += formatLine('Layanan Bandara', formData.packagePricing.airportService);
        message += formatLine('Al Baik', formData.packagePricing.withAlBaik ? 'Ya' : 'Tidak');
    }

    // Optional Addons
    if (summary.addons.length > 0) {
        message += formatHeader('5. Tambahan Opsional');
        if (formData.optionalAddons.haramain_train) message += `- Tiket Kereta Haramain\n`;
        if (formData.optionalAddons.thaif_tour) message += `- Tour Thaif Full Package\n`;
        if (formData.optionalAddons.mutawwif) message += `- Mutawwif Tambahan (${formData.optionalAddons.mutawwif_days} hari)\n`;
        if (formData.optionalAddons.tasrih_raudhah) message += `- Tasrih Raudhah\n`;
        if (formData.optionalAddons.uniform_bag) message += `- Seragam & Tas Jamaah\n`;
        if (formData.optionalAddons.documentation_drone) message += `- Dokumentasi / Drone\n`;
    }

    // Flight Details
    message += formatHeader('6. Detail Penerbangan');
    message += formatLine('Tanggal Keberangkatan', formData.flightDetails.departureDate ? format(new Date(formData.flightDetails.departureDate), "d MMMM yyyy", { locale: id }) : 'N/A');
    message += formatLine('Tanggal Kepulangan', formData.flightDetails.returnDate ? format(new Date(formData.flightDetails.returnDate), "d MMMM yyyy", { locale: id }) : 'N/A');
    message += formatLine('Maskapai', formData.flightDetails.airline);
    message += formatLine('No. Penerbangan', formData.flightDetails.flightNumber);
    message += formatLine('Status Tiket', formData.flightDetails.ticketStatus);

    // Hotel Details
    if (formData.hotelDetails.hotels.length > 0 && formData.hotelDetails.hotels[0].hotelName) {
        message += formatHeader('7. Detail Hotel');
        formData.hotelDetails.hotels.forEach((hotel, index) => {
            message += `*Hotel ${index + 1}:*\n`;
            message += formatLine(' - Nama Hotel', hotel.hotelName);
            message += formatLine(' - Kota', hotel.city);
            message += formatLine(' - Check-in', hotel.checkIn ? format(new Date(hotel.checkIn), "d MMMM yyyy", { locale: id }) : 'N/A');
            message += formatLine(' - Jumlah Malam', hotel.nights);
            message += formatLine(' - Check-out', hotel.checkOut ? format(new Date(hotel.checkOut), "d MMMM yyyy", { locale: id }) : 'N/A');
        });
    }

    // Visa Status
    message += formatHeader('8. Status Visa');
    message += `${formData.visaStatus}\n`;

    // Mutawwif Request
    if (formData.mutawwifRequest.needed === 'Perlu') {
        message += formatHeader('9. Permintaan Mutawwif');
        message += `*Gaya:* ${formData.mutawwifRequest.style.join(', ')}\n`;
        message += `*Kemampuan:* ${formData.mutawwifRequest.skills.join(', ')}\n`;
        message += `*Bahasa:* ${formData.mutawwifRequest.languages.join(', ')}\n`;
        message += `*Gender:* ${formData.mutawwifRequest.gender}\n`;
    }
    
    // Summary
    message += formatHeader('Ringkasan Biaya');
    message += formatLine('Subtotal Paket', formatCurrency(summary.subtotal));
    summary.addons.forEach(addon => {
        message += formatLine(` - ${addon.name}`, formatCurrency(addon.cost));
    });
    message += `--------------------------\n`;
    message += formatLine('Estimasi Total', formatCurrency(summary.total));

    message += `\nMohon konfirmasi dan informasi selanjutnya. Terima kasih.`;

    return `https://wa.me/${WHATSAPP_NUMBER}?text=${encodeURIComponent(message)}`;
};
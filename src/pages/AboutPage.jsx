import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import AboutHero from '@/components/about/AboutHero';
import AboutContent from '@/components/about/AboutContent';
import WhyChooseUs from '@/components/about/WhyChooseUs';
import OurStory from '@/components/about/OurStory';
import OurValues from '@/components/about/OurValues';
import PartnerLogos from '@/components/shared/PartnerLogos';
import CallToAction from '@/components/home/<USER>';
import { Button } from '@/components/ui/button.jsx';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { MessageSquare } from 'lucide-react';
import SEO from '@/components/shared/SEO.jsx';
import { LanguageContext } from '@/contexts/LanguageContext';

const AboutPage = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const pageTitle = getTranslation('aboutPageTitle', 'Tentang Kami');
  const pageDescription = getTranslation('aboutPageSubtitle', 'Kenali lebih dekat komitmen dan profesionalisme kami dalam melayani tamu Allah.');

  return (
    <>
      <SEO
        title={pageTitle}
        description={pageDescription}
      />
      <div className="flex flex-col min-h-screen bg-background">
        <Navbar />
        <main className="grow">
          <AboutHero />
          <AboutContent />
          <WhyChooseUs />
          <OurStory />
          <OurValues />
          <section id="testimonials" className="py-20 bg-secondary">
            <div className="container mx-auto px-4 md:px-6 text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <MessageSquare className="w-12 h-12 mx-auto mb-5 text-[#FFD700]" />
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  {getTranslation('aboutCTASectionTitle', 'Apa Kata Mitra & Jamaah Kami?')}
                </h2>
                <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto">
                  {getTranslation('aboutCTASectionDesc', 'Kami berdedikasi untuk memberikan pengalaman terbaik. Lihat testimoni dari mereka yang telah mempercayakan kami.')}
                </p>
                <Button asChild size="lg" className="gold-gradient text-black font-semibold px-8 py-3 text-lg group">
                  <Link to="/testimonials">
                    {getTranslation('aboutCTASectionButton', 'Lihat Semua Testimoni')}
                    <MessageSquare className="ml-2.5 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
              </motion.div>
            </div>
          </section>
          <PartnerLogos />
          <CallToAction />
        </main>
        <Footer />
      </div>
    </>
  );
};

export default AboutPage;
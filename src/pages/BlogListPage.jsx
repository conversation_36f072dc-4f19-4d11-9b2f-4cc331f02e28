import React, { useState, useContext, useMemo, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { motion } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { useBlog } from '@/contexts/BlogContext.jsx';
import SEO from '@/components/shared/SEO.jsx';
import BlogToolbar from '@/components/blog/BlogToolbar.jsx';
import BlogCard from '@/components/blog/BlogCard.jsx';
import BlogListItem from '@/components/blog/BlogListItem.jsx';
import BlogPagination from '@/components/blog/BlogPagination.jsx';
import { getLocale } from '@/utils/autoTranslate.js';

const BlogListPage = () => {
  const { posts: translatedPosts, isLoading, blogSettings, updateBlogSettings } = useBlog();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState('grid');
  const { translations, language } = useContext(LanguageContext);
  const placeholderImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg"; // Using the provided image as placeholder
  const [locale, setLocale] = useState(getLocale('id'));

  useEffect(() => {
    setLocale(getLocale(language));
  }, [language]);
  
  const publishedPosts = useMemo(() => {
    return translatedPosts
      .filter(post => post.status === 'Published' && new Date(post.publish_date) <= new Date())
      .sort((a, b) => new Date(b.publish_date) - new Date(a.publish_date));
  }, [translatedPosts]);

  const categories = useMemo(() => {
    const uniqueCategories = new Set(publishedPosts.map(p => p.category).filter(Boolean));
    return ['all', ...Array.from(uniqueCategories)];
  }, [publishedPosts]);

  const filteredPosts = useMemo(() => {
    let tempPosts = publishedPosts;
    if (searchTerm) {
      tempPosts = tempPosts.filter(post =>
        (post.title && post.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (post.content && post.content.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    if (filterCategory !== 'all') {
      tempPosts = tempPosts.filter(post => post.category === filterCategory);
    }
    return tempPosts;
  }, [searchTerm, filterCategory, publishedPosts]);
  
  const postsPerPage = blogSettings.postsPerPage || 6;
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPosts = filteredPosts.slice(startIndex, endIndex);
  
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterCategory, postsPerPage]);

  const formatDate = (dateString) => {
    if (!dateString) return translations.dateNotAvailable || 'Tanggal tidak tersedia';
    return new Date(dateString).toLocaleDateString(language, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  
  const getExcerpt = (htmlContent, maxLength = 120) => {
    if (!htmlContent) return translations.noDescriptionAvailable || "Deskripsi tidak tersedia.";
    const textContent = htmlContent.replace(/<[^>]+>/g, '');
    if (textContent.length <= maxLength) return textContent;
    return textContent.substring(0, maxLength) + '...';
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 200, behavior: 'smooth' });
  };
  
  const handleClearFilters = () => {
    setSearchTerm('');
    setFilterCategory('all');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.07 } },
  };
  
  const pageTitle = translations.blogPageTitle || "Blog & Artikel";
  const pageDescription = translations.blogPageSubtitle || "Jelajahi wawasan, tips, dan berita terbaru dari kami.";

  return (
    <>
      <SEO
        title={pageTitle}
        description={pageDescription}
      />
      <div className="min-h-screen flex flex-col bg-background text-white">
        <Navbar />
        <main className="grow pt-28 pb-16">
          <div className="container mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-12"
            >
              <h1 className="text-4xl md:text-5xl font-extrabold gold-gradient-text mb-4">
                {pageTitle}
              </h1>
              <p className="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto">
                {pageDescription}
              </p>
            </motion.div>

            <BlogToolbar
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filterCategory={filterCategory}
              onCategoryChange={setFilterCategory}
              categories={categories}
              translations={translations}
              onClearFilters={handleClearFilters}
              hasActiveFilters={searchTerm !== '' || filterCategory !== 'all'}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              postsPerPage={postsPerPage}
              onPostsPerPageChange={(value) => updateBlogSettings({ postsPerPage: value })}
            />

            {isLoading ? (
               <motion.div className="text-center text-gray-400 text-xl py-10">
                {translations.loadingArticles || 'Memuat artikel...'}
              </motion.div>
            ) : filteredPosts.length === 0 ? (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center text-gray-400 text-xl py-10 bg-gray-800/30 rounded-lg"
              >
                <p className="font-semibold text-white mb-2">{translations.noResultsFound || "Tidak Ada Artikel yang Ditemukan"}</p>
                <p className="text-sm">{translations.tryDifferentFilters || "Coba ubah filter atau kata kunci pencarian Anda."}</p>
              </motion.div>
            ) : (
              <>
                <motion.div
                  className={viewMode === 'grid' 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                    : "flex flex-col gap-8"
                  }
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  {currentPosts.map((post) => (
                    viewMode === 'grid' ? (
                      <BlogCard 
                        key={`${post.id}-grid`}
                        post={post}
                        formatDate={formatDate}
                        getExcerpt={getExcerpt}
                        blogSettings={blogSettings}
                        translations={translations}
                        placeholderImage={placeholderImage}
                      />
                    ) : (
                      <BlogListItem
                        key={`${post.id}-list`}
                        post={post}
                        formatDate={formatDate}
                        getExcerpt={getExcerpt}
                        blogSettings={blogSettings}
                        translations={translations}
                        placeholderImage={placeholderImage}
                      />
                    )
                  ))}
                </motion.div>
                
                <BlogPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                  translations={translations}
                />
              </>
            )}
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default BlogListPage;
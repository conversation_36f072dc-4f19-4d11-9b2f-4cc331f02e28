import React, { useContext, useMemo, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { useParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { ArrowLeft, CalendarDays, UserCircle, Tag, Eye } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { useBlog } from '@/contexts/BlogContext.jsx';
import SEO from '@/components/shared/SEO.jsx';
import BlogSidebar from '@/components/blog/BlogSidebar.jsx';
import SocialShare from '@/components/blog/SocialShare.jsx';
import NotFoundPage from '@/pages/NotFoundPage.jsx';

const BlogPostPage = () => {
  const { slug } = useParams();
  const { posts: translatedPosts, isLoading, blogSettings, incrementViewCount, getPostBySlug } = useBlog();
  const { translations, language } = useContext(LanguageContext);

  const post = useMemo(() => getPostBySlug(slug), [getPostBySlug, slug]);
  const placeholderImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg";

  const { relatedPosts, allCategories } = useMemo(() => {
    const publishedPosts = translatedPosts.filter(p => p.status === 'Published' && new Date(p.publish_date) <= new Date());
    
    let related = [];
    if (post) {
      related = publishedPosts
        .filter(p => p.id !== post.id && p.category === post.category)
        .slice(0, 3);
    }

    const categoriesCount = publishedPosts.reduce((acc, p) => {
      if (p.category) {
        acc[p.category] = (acc[p.category] || 0) + 1;
      }
      return acc;
    }, {});

    const sortedCategories = Object.entries(categoriesCount)
      .sort(([, a], [, b]) => b - a)
      .map(([name, count]) => ({ name, count }));
    
    return { relatedPosts: related, allCategories: sortedCategories };
  }, [translatedPosts, post]);

  useEffect(() => {
    if (post && post.id) {
      incrementViewCount(post.id);
    }
  }, [post, incrementViewCount]);

  const formatDate = (dateString) => {
    if (!dateString) return translations.dateNotAvailable || 'Tanggal tidak tersedia';
    try {
      return new Date(dateString).toLocaleDateString(language, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };
  
  const formatViews = (views) => {
    if (typeof views !== 'number') return '0';
    if (views >= 1000) {
      return (views / 1000).toFixed(1) + 'k';
    }
    return views.toString();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-background text-white">
        <Navbar />
        <main className="grow pt-28 pb-16 flex items-center justify-center">
          <p className="text-xl text-gray-400">{translations.loadingArticle || "Memuat artikel..."}</p>
        </main>
        <Footer />
      </div>
    );
  }

  if (!post) {
    return <NotFoundPage />;
  }

  const postUrl = `https://www.umrahservice.co/blog/${post.slug}`;
  const metaDescription = post.meta?.description || post.content.replace(/<[^>]+>/g, '').substring(0, 160) + '...';
  const metaTitle = post.meta?.title || post.title;
  const postKeywords = post.tags && Array.isArray(post.tags) ? post.tags : [];

  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": postUrl
    },
    "headline": metaTitle,
    "description": metaDescription,
    "image": post.featured_image || placeholderImage,
    "author": {
      "@type": "Organization",
      "name": post.author || "Arrahmah Handling Service"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Arrahmah Handling Service",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.umrahservice.co/logo-umrahservice.png"
      }
    },
    "datePublished": new Date(post.publish_date).toISOString(),
    "dateModified": new Date(post.updated_at || post.publish_date).toISOString()
  };
  
  return (
    <>
      <SEO
        title={metaTitle}
        description={metaDescription}
        keywords={postKeywords.join(', ')}
        ogImage={post.featured_image || placeholderImage}
        ogType="article"
        articlePublishedTime={post.publish_date}
        articleAuthor={post.author || "Admin"}
        articleTags={postKeywords}
        schema={articleSchema}
      />
      <div className="min-h-screen flex flex-col bg-background text-white">
        <Navbar />
        <main className="grow pt-28 pb-16">
          <div className="container mx-auto px-4 md:px-6">
            <div className="mb-8">
              <Button asChild variant="outline" className="text-primary border-primary hover:bg-primary/10 hover:text-primary">
                <Link to="/blog"><ArrowLeft size={18} className="mr-2" /> {translations.backToBlog || "Kembali ke Blog"}</Link>
              </Button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12">
              <motion.article
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="lg:col-span-8"
              >
                <header className="mb-10">
                  <h1 className="text-4xl md:text-5xl font-extrabold gradient-text mb-6 leading-tight">{post.title}</h1>
                  <div className="flex flex-wrap items-center text-gray-400 text-sm gap-x-4 gap-y-2 mb-6">
                    {blogSettings.showDate && (<span className="flex items-center"><CalendarDays size={16} className="mr-1.5 text-primary" /> {formatDate(post.publish_date)}</span>)}
                    {blogSettings.showAuthor && (<span className="flex items-center"><UserCircle size={16} className="mr-1.5 text-primary" /> {post.author || "Admin"}</span>)}
                    {blogSettings.showCategory && post.category && (<span className="flex items-center"><Tag size={16} className="mr-1.5 text-primary" /> {post.category}</span>)}
                    {blogSettings.showViews && (
                      <span className="flex items-center">
                        <Eye size={16} className="mr-1.5 text-primary" /> 
                        {formatViews(post.views)} {translations.views || 'dilihat'}
                      </span>
                    )}
                  </div>
                </header>

                {(post.featured_image || placeholderImage) && (
                  <div className="mb-10 rounded-xl overflow-hidden shadow-xl aspect-video max-h-[500px]">
                    <img  className="w-full h-full object-cover" alt={post.title || "Gambar utama artikel"} src={post.featured_image || placeholderImage} loading="eager" />
                  </div>
                )}

                <div 
                  className="prose prose-lg prose-invert max-w-none mx-auto text-gray-300"
                  style={{ '--tw-prose-body': '#d1d5db', '--tw-prose-headings': '#FFD700', '--tw-prose-lead': '#e5e7eb', '--tw-prose-links': '#FFD700', '--tw-prose-bold': '#FFD700', '--tw-prose-counters': '#9ca3af', '--tw-prose-bullets': '#facc15', '--tw-prose-hr': 'rgba(255, 255, 255, 0.2)', '--tw-prose-quotes': '#f3f4f6', '--tw-prose-quote-borders': '#FFD700', '--tw-prose-captions': '#9ca3af', '--tw-prose-code': '#e5e7eb', '--tw-prose-pre-code': '#e5e7eb', '--tw-prose-pre-bg': 'rgba(0, 0, 0, 0.2)', lineHeight: '1.8', fontSize: '18px' }}
                  dangerouslySetInnerHTML={{ __html: post.content || `<p>${translations.contentNotAvailable || "Konten tidak tersedia."}</p>` }}
                />
                
                {postKeywords.length > 0 && (
                  <div className="mt-12 pt-8 border-t border-gray-700/50">
                    <h3 className="text-lg font-semibold text-white mb-4">{translations.articleTags || 'Tag Artikel'}:</h3>
                    <div className="flex flex-wrap gap-2">
                      {postKeywords.map(tag => (
                        <span key={tag} className="bg-gray-700 text-gray-300 text-sm font-medium px-3 py-1.5 rounded-full">{tag}</span>
                      ))}
                    </div>
                  </div>
                )}

                {blogSettings.showSharingButtons && (
                   <SocialShare title={post.title} url={postUrl} />
                )}
              </motion.article>

              <aside className="lg:col-span-4 lg:mt-24">
                <BlogSidebar relatedPosts={relatedPosts} categories={allCategories} formatDate={formatDate} currentPostId={post.id}/>
              </aside>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default BlogPostPage;
import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import Hero from '@/components/home/<USER>';
import Features from '@/components/home/<USER>';
import CallToAction from '@/components/home/<USER>';
import PartnerLogos from '@/components/shared/PartnerLogos';
import BlogSection from '@/components/home/<USER>';
import WhatIsLA from '@/components/home/<USER>';
import InstagramFeed from '@/components/home/<USER>';
import Testimonials from '@/components/home/<USER>';
import SEO from '@/components/shared/SEO.jsx';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const HomePage = () => {
  const { translations } = useContext(LanguageContext);

  const getTranslation = (key, fallback) => translations[key] || fallback;

  const pageTitle = getTranslation('seoHomePageTitle', 'Handling Umrah & Land Arrangement Umroh Profesional');
  const pageDescription = getTranslation('seoMetaDescription', 'Jasa handling umrah & land arrangement umroh terbaik untuk travel agent & jamaah. UmrahService.co adalah mitra handling umroh terpercaya di Makkah & Madinah.');
  const pageKeywords = getTranslation('seoKeywords', 'umrah, umroh, handling umrah, handling umroh, land arrangement umrah, land arrangement umroh, jasa handling umrah, layanan umrah profesional');

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "UmrahService.co",
    "url": "https://www.umrahservice.co",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://www.umrahservice.co/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  const travelAgencySchema = {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "UmrahService.co",
    "description": pageDescription,
    "url": "https://www.umrahservice.co",
    "logo": "https://www.umrahservice.co/logo-umrahservice.png",
    "image": "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg",
    "telephone": "+966540705271",
    "priceRange": "$",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al Imam Muslim St, Az Zahir",
      "addressLocality": "Makkah",
      "addressCountry": "SA"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+6281280908093",
      "contactType": "Customer Service",
      "areaServed": "ID",
      "availableLanguage": ["Indonesian", "English", "Arabic"]
    },
    "sameAs": [
      "https://www.instagram.com/umrohserviceco/",
      "https://facebook.com/umrohserviceco/",
      "https://x.com/umrahserviceco",
      "https://www.tiktok.com/@umrahserviceco"
    ]
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <SEO
        title={pageTitle}
        description={pageDescription}
        keywords={pageKeywords}
        schema={[websiteSchema, travelAgencySchema]}
      />
      <Navbar />
      <main className="grow">
        <Hero />
        <PartnerLogos />
        <WhatIsLA />
        <Features />
        <Testimonials />
        <BlogSection />
        <InstagramFeed />
        <CallToAction />
      </main>
      <Footer />
    </div>
  );
};

export default HomePage;
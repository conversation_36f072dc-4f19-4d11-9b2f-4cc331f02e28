import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import SEO from '@/components/shared/SEO';
import { motion } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckCircle, Hotel, Bus, Users, ShieldCheck, Cpu, MessageSquare, ChevronsRight } from 'lucide-react';

const LandArrangementPage = () => {
    const { translations } = useContext(LanguageContext);
    const getTranslation = (key, fallback = '') => translations[key] || fallback;

    const pageTitle = getTranslation("laPageTitle", "Land Arrangement Umrah Resmi & Terpercaya | Arrahmah");
    const pageDescription = getTranslation("laPageDescription", "Butuh layanan Land Arrangement Umrah tanpa ribet? Arrahmah siap bantu visa, hotel, bus, handling, mutawwif — lengkap dan legal. Cek paketnya sekarang!");
    const pageKeywords = "Land Arrangement Umrah, Land Umrah, Land Umroh, Paket Land Arrangement Umrah, Layanan Land Umrah Resmi, Jasa LA Umrah, Handling Umrah, Ground handling Umrah, Paket Umrah tanpa tiket, Umrah Mandiri, Jasa visa dan hotel Umrah, Umrah tanpa travel, Paket Umrah B2B";
    const pageImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/50b2890a72b6acf1ba8ca2a29469917a.jpg";

    const benefits = [
        { icon: <Hotel className="w-10 h-10 mb-4 text-[#FFD700]" />, titleKey: 'benefit1Title', descKey: 'benefit1Desc' },
        { icon: <ShieldCheck className="w-10 h-10 mb-4 text-[#FFD700]" />, titleKey: 'benefit2Title', descKey: 'benefit2Desc' },
        { icon: <Bus className="w-10 h-10 mb-4 text-[#FFD700]" />, titleKey: 'benefit3Title', descKey: 'benefit3Desc' },
        { icon: <Users className="w-10 h-10 mb-4 text-[#FFD700]" />, titleKey: 'benefit4Title', descKey: 'benefit4Desc' },
        { icon: <Users className="w-10 h-10 mb-4 text-[#FFD700]" />, titleKey: 'benefit5Title', descKey: 'benefit5Desc' },
        { icon: <Cpu className="w-10 h-10 mb-4 text-[#FFD700]" />, titleKey: 'benefit6Title', descKey: 'benefit6Desc' },
    ];

    const faqItems = [
        { qKey: "faq1q", aKey: "faq1a" },
        { qKey: "faq2q", aKey: "faq2a" },
        { qKey: "faq3q", aKey: "faq3a" },
        { qKey: "faq4q", aKey: "faq4a" },
    ];
    
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: { staggerChildren: 0.1, delayChildren: 0.2 },
        },
    };
    
    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 100 } },
    };

    return (
        <>
            <SEO
                title={pageTitle}
                description={pageDescription}
                keywords={pageKeywords}
                ogImage={pageImage}
            />
            <div className="flex flex-col min-h-screen bg-background text-white">
                <Navbar />
                <main className="grow">
                    <section className="relative bg-linear-to-b from-gray-900 to-background pt-32 pb-20">
                        <div className="container mx-auto px-4 md:px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="grid md:grid-cols-2 gap-12 items-center"
                            >
                                <div className="text-center md:text-left">
                                    <h1 className="text-4xl md:text-5xl font-extrabold mb-6 tracking-tight gradient-text">{getTranslation("laHeroTitle", "Land Arrangement (LA) Umrah & Terpercaya")}</h1>
                                    <p className="text-lg md:text-xl text-gray-300 mb-8 leading-relaxed">{getTranslation("laHeroSubtitle", "Solusi Terpadu untuk Travel Agent dan Jamaah Umrah private. Kami menghadirkan layanan Land Arrangement profesional yang mencakup akomodasi hotel, transportasi, visa umrah, mutawwif berpengalaman, serta handling bandara dan hotel — tanpa termasuk tiket pesawat.")}</p>
                                    <Button asChild size="lg" className="gold-gradient text-black font-semibold px-8 py-3 text-lg group">
                                        <Link to="/pricing">
                                            {getTranslation("laViewPackagesButton", "Lihat Paket & Harga")}
                                            <ChevronsRight className="ml-2.5 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                                        </Link>
                                    </Button>
                                </div>
                                <motion.div 
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.8, delay: 0.2 }}
                                    className="hidden md:block"
                                >
                                    <img alt={getTranslation("laOwnerImageAlt", "Foto pemilik Arrahmah Handling Service")} className="rounded-xl shadow-2xl border-2 border-primary/20" src={pageImage} />
                                </motion.div>
                            </motion.div>
                        </div>
                    </section>

                    <section className="py-20 bg-secondary">
                        <div className="container mx-auto px-4 md:px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true, amount: 0.5 }}
                                transition={{ duration: 0.7 }}
                                className="text-center max-w-3xl mx-auto mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-4">{getTranslation("laBenefitsTitle", "Keunggulan Paket Land Umrah dari Arrahmah")}</h2>
                                <p className="text-lg text-gray-400" dangerouslySetInnerHTML={{ __html: getTranslation("laBenefitsSubtitle", "Di UmrahService.co, kami menyediakan layanan <strong>Land Umrah lengkap dan resmi</strong> melalui Arrahmah Handling Service yang telah berpengalaman lebih dari 8 tahun dan didukung Muassasah resmi Saudi.") }} />
                            </motion.div>
                            <motion.div
                                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
                                variants={containerVariants}
                                initial="hidden"
                                whileInView="visible"
                                viewport={{ once: true, amount: 0.1 }}
                            >
                                {benefits.map((benefit, index) => (
                                    <motion.div
                                        key={index}
                                        className="bg-linear-to-br from-gray-800 to-gray-900 p-8 rounded-xl shadow-lg border border-gray-700/50 text-center transform hover:-translate-y-1 transition-transform duration-300"
                                        variants={itemVariants}
                                    >
                                        <div className="flex justify-center items-center mb-4">{benefit.icon}</div>
                                        <h3 className="text-xl font-bold text-white mb-3">{getTranslation(benefit.titleKey)}</h3>
                                        <p className="text-gray-400 leading-relaxed text-sm">{getTranslation(benefit.descKey)}</p>
                                    </motion.div>
                                ))}
                            </motion.div>
                        </div>
                    </section>
                    
                    <section className="py-20 bg-background">
                        <div className="container mx-auto px-4 md:px-6">
                             <div className="grid md:grid-cols-2 gap-12 items-center">
                                <motion.div
                                    initial={{ opacity: 0, x: -50 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.8 }}
                                >
                                     <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-6">{getTranslation("laWhoIsItForTitle", "Siapa yang Cocok Menggunakan Jasa LA Umrah?")}</h2>
                                     <p className="text-lg text-gray-300 mb-6 leading-relaxed">{getTranslation("laWhoIsItForSubtitle", "Layanan Land Arrangement (LA) kami dirancang fleksibel untuk memenuhi kebutuhan beragam klien, memastikan setiap perjalanan umrah berjalan lancar dan efisien.")}</p>
                                     <ul className="space-y-4">
                                        <li className="flex items-start"><CheckCircle className="w-6 h-6 text-primary mr-3 mt-1 shrink-0" /><span className="text-gray-200 text-lg" dangerouslySetInnerHTML={{ __html: getTranslation("who1") }}></span></li>
                                        <li className="flex items-start"><CheckCircle className="w-6 h-6 text-primary mr-3 mt-1 shrink-0" /><span className="text-gray-200 text-lg" dangerouslySetInnerHTML={{ __html: getTranslation("who2") }}></span></li>
                                        <li className="flex items-start"><CheckCircle className="w-6 h-6 text-primary mr-3 mt-1 shrink-0" /><span className="text-gray-200 text-lg" dangerouslySetInnerHTML={{ __html: getTranslation("who3") }}></span></li>
                                     </ul>
                                </motion.div>
                                <motion.div
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    whileInView={{ opacity: 1, scale: 1 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.8, delay: 0.2 }}
                                    className="hidden md:block"
                                >
                                    <img  alt={getTranslation("laWhoIsItForImageAlt", "Presentasi layanan untuk travel agent")} className="rounded-xl shadow-2xl" src="https://images.unsplash.com/photo-1624864005286-87dd57af16f3" />
                                </motion.div>
                             </div>
                        </div>
                    </section>

                    <section className="py-20 bg-secondary">
                        <div className="container mx-auto px-4 md:px-6">
                            <motion.div
                                className="text-center max-w-3xl mx-auto mb-12"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.7 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-4">{getTranslation("laFaqTitle", "Pertanyaan Umum (FAQ)")}</h2>
                                <p className="text-lg text-gray-400">{getTranslation("laFaqSubtitle", "Temukan jawaban untuk pertanyaan yang paling sering diajukan mengenai layanan Land Arrangement Umrah kami.")}</p>
                            </motion.div>
                            <motion.div 
                                className="max-w-3xl mx-auto"
                                initial={{ opacity: 0 }}
                                whileInView={{ opacity: 1 }}
                                viewport={{ once: true, amount: 0.2 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                            >
                                <Accordion type="single" collapsible className="w-full space-y-4">
                                    {faqItems.map((item, index) => (
                                        <AccordionItem key={index} value={`item-${index}`} className="bg-linear-to-br from-gray-800 to-gray-900 border border-gray-700/70 rounded-lg shadow-lg overflow-hidden">
                                            <AccordionTrigger className="flex items-center justify-between w-full p-6 text-left text-lg font-medium text-white hover:no-underline hover:bg-white/5 transition-colors">
                                                <span>{getTranslation(item.qKey)}</span>
                                            </AccordionTrigger>
                                            <AccordionContent className="p-6 pt-0 text-gray-300 text-base leading-relaxed">
                                                <p>{getTranslation(item.aKey)}</p>
                                            </AccordionContent>
                                        </AccordionItem>
                                    ))}
                                </Accordion>
                            </motion.div>
                        </div>
                    </section>

                    <section className="py-20 bg-background text-center">
                        <div className="container mx-auto px-4 md:px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.8, type: 'spring' }}
                                className="max-w-3xl mx-auto"
                            >
                                <MessageSquare className="w-12 h-12 mx-auto mb-5 text-primary" />
                                <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-4">{getTranslation("laCTATitle", "Siap Meningkatkan Kualitas Layanan Umrah Anda?")}</h2>
                                <p className="text-lg text-gray-300 mb-8">{getTranslation("laCTASubtitle", "Diskusikan kebutuhan Land Arrangement Anda dengan tim ahli kami. Dapatkan penawaran B2B terbaik yang dirancang khusus untuk travel Anda.")}</p>
                                <Button asChild size="lg" className="gold-gradient text-black font-semibold px-8 py-3 text-lg group">
                                    <Link to="/contact">
                                        {getTranslation("laCTAButton", "Hubungi Kami Sekarang")}
                                        <MessageSquare className="ml-2.5 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                                    </Link>
                                </Button>
                            </motion.div>
                        </div>
                    </section>

                </main>
                <Footer />
            </div>
        </>
    );
};

export default LandArrangementPage;
import React, { useState, useEffect, useCallback, useContext } from 'react';
import SEO from '@/components/shared/SEO';
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import OrderForm from '@/components/order/OrderForm';
import OrderSummary from '@/components/order/OrderSummary';
import PackageDetailSidebar from '@/components/order/PackageDetailSidebar';
import { Button } from "@/components/ui/button";
import { Send, Layers } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useNavigate } from 'react-router-dom';
import { useOrderForm } from '@/hooks/useOrderForm';
import { useOrderSummary } from '@/hooks/useOrderSummary';
import { orderStorageService } from '@/services/orderStorageService';

const OrderPage = () => {
    const { toast } = useToast();
    const navigate = useNavigate();
    const { formData, setFormData, handleInputChange, onPackageTypeChange, onSelectLaB2bPackage } = useOrderForm();
    const { summary } = useOrderSummary(formData);
    const [hoveredPackage, setHoveredPackage] = useState(null);
    const { translations } = useContext(LanguageContext);
    const getTranslation = (key, fallback) => translations[key] || fallback;

    const handleSubmit = (e) => {
        e.preventDefault();
        
        try {
            const newOrder = orderStorageService.addOrder(formData, summary);
            toast({
                title: "Order Berhasil Diterima!",
                description: "Anda akan diarahkan ke halaman detail pesanan.",
            });
            navigate(`/order/success/${newOrder.id}`);
        } catch (error) {
            toast({
                title: "Gagal Mencatat Order",
                description: "Terjadi kesalahan saat menyimpan pesanan.",
                variant: "destructive",
            });
        }
    };
    
    const sidebarPackage = hoveredPackage || formData.selectedLaB2bPackage;

    return (
        <>
            <SEO
                title="Formulir Pemesanan Layanan"
                description="Pesan layanan handling umrah, visa, hotel, dan lainnya melalui formulir pemesanan resmi Arrahmah Handling Service."
                keywords="pesan handling umrah, order land arrangement, formulir visa umrah, booking layanan umrah"
            />
            <Navbar />
            <motion.main
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-background pt-32 pb-20"
            >
                <div className="container mx-auto px-4 md:px-6">
                     <Card className="bg-linear-to-br from-gray-800/60 to-gray-900/80 border-gray-700/60 rounded-3xl shadow-2xl overflow-hidden backdrop-blur-xs mb-12">
                        <CardHeader className="text-center pb-4">
                            <Layers className="mx-auto h-12 w-12 text-amber-400" />
                            <CardTitle className="text-4xl font-bold text-white tracking-tight mt-4">Formulir Pemesanan Layanan</CardTitle>
                            <CardDescription className="text-lg text-gray-400 max-w-3xl mx-auto pt-2">
                               Pilih jenis layanan yang Anda butuhkan. Formulir akan menyesuaikan secara dinamis untuk mempermudah pemesanan Anda.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                        <div className="lg:col-span-2 space-y-8">
                           <OrderForm 
                             formData={formData} 
                             handleInputChange={handleInputChange}
                             setFormData={setFormData}
                             onPackageTypeChange={onPackageTypeChange}
                             onSelectLaB2bPackage={onSelectLaB2bPackage}
                             setHoveredPackage={setHoveredPackage}
                           />
                        </div>

                        <div className="lg:col-span-1 lg:sticky lg:top-28 flex flex-col" style={{ height: 'calc(100vh - 7rem)' }}>
                             <div className="grow overflow-y-auto pr-2">
                                <div className="space-y-6">
                                    <OrderSummary summary={summary} formData={formData} />
                                    {formData.packageType === 'la_b2b' && sidebarPackage && (
                                        <PackageDetailSidebar pkg={sidebarPackage} translations={translations} />
                                    )}
                                </div>
                            </div>
                            <div className="shrink-0 pt-4 bg-background">
                                <Button type="submit" size="lg" className="w-full gold-gradient text-black font-bold text-lg py-6" disabled={formData.totalPax === 0}>
                                    <Send className="mr-2 h-5 w-5"/>
                                    Kirim & Lihat Detail Order
                                </Button>
                            </div>
                        </div>
                    </form>
                </div>
            </motion.main>
            <Footer />
        </>
    );
};

export default OrderPage;
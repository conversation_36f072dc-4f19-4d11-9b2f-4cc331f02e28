import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import SEO from '@/components/shared/SEO';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, X, Maximize, Minimize } from 'lucide-react';
import { slidesData } from '@/components/presentation/slidesData';
import SlideComponent from '@/components/presentation/SlideComponent';

const PresentationPage = () => {
    const [[page, direction], setPage] = useState([0, 0]);
    const navigate = useNavigate();
    const [isFullscreen, setIsFullscreen] = useState(false);

    const paginate = (newDirection) => {
        const nextPage = page + newDirection;
        if (nextPage >= 0 && nextPage < slidesData.length) {
            setPage([nextPage, newDirection]);
        }
    };

    const handleFullscreen = () => {
        const elem = document.documentElement;
        if (!document.fullscreenElement) {
            elem.requestFullscreen().catch(err => {
                alert(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
            });
        } else {
            document.exitFullscreen();
        }
    };

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === 'ArrowRight') {
                paginate(1);
            } else if (e.key === 'ArrowLeft') {
                paginate(-1);
            } else if (e.key === 'Escape') {
                navigate('/about');
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [page, navigate]);

    useEffect(() => {
        const onFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };
        document.addEventListener('fullscreenchange', onFullscreenChange);
        return () => document.removeEventListener('fullscreenchange', onFullscreenChange);
    }, []);
    
    return (
        <>
            <SEO
                title="Kemitraan Profesional Arrahmah Handling Service untuk Travel B2B"
                description="Presentasi interaktif mengenai kemitraan profesional Arrahmah Handling Service untuk travel B2B."
            />
            <div className="bg-gray-900 text-white w-screen h-screen overflow-hidden flex flex-col bg-cover bg-center font-sans" style={{backgroundImage: `url('https://images.unsplash.com/photo-1598435349552-2d8433115c17?q=80&w=2070&auto=format&fit=crop')`}}>
                <div className="absolute inset-0 bg-black/60 backdrop-blur-xs"></div>
                <div className="relative grow flex items-center justify-center">
                    <AnimatePresence initial={false} custom={direction}>
                        <SlideComponent key={page} slide={slidesData[page]} direction={direction} />
                    </AnimatePresence>
                </div>
                
                <div className="absolute top-4 right-4 z-20 flex space-x-2">
                     <Button variant="ghost" size="icon" onClick={handleFullscreen} className="text-white hover:bg-white/20">
                        {isFullscreen ? <Minimize className="h-6 w-6" /> : <Maximize className="h-6 w-6" />}
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => navigate('/about')} className="text-white hover:bg-white/20">
                        <X className="h-6 w-6" />
                    </Button>
                </div>

                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-20 flex items-center space-x-4">
                    <Button variant="outline" size="icon" onClick={() => paginate(-1)} disabled={page === 0} className="bg-black/50 border-white/50 text-white hover:bg-white/20">
                        <ChevronLeft className="h-6 w-6" />
                    </Button>
                     <span className="text-gray-200 text-sm font-mono bg-black/50 px-3 py-1 rounded-md">{`${page + 1} / ${slidesData.length}`}</span>
                    <Button variant="outline" size="icon" onClick={() => paginate(1)} disabled={page === slidesData.length - 1} className="bg-black/50 border-white/50 text-white hover:bg-white/20">
                        <ChevronRight className="h-6 w-6" />
                    </Button>
                </div>
                 <div className="absolute bottom-0 left-0 w-full h-1.5 bg-gray-700/50 z-10">
                    <motion.div
                        className="h-full bg-linear-to-r from-amber-400 to-yellow-500"
                        initial={{ width: '0%' }}
                        animate={{ width: `${((page + 1) / slidesData.length) * 100}%` }}
                        transition={{ duration: 0.5, ease: 'easeInOut' }}
                    />
                </div>
            </div>
        </>
    );
};

export default PresentationPage;
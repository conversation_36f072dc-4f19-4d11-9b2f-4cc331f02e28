import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import PricingTables from '@/components/pricing/PricingTables';
import SEO from '@/components/shared/SEO.jsx';
import { LanguageContext } from '@/contexts/LanguageContext';
import { CurrencyProvider } from '@/contexts/CurrencyContext';

const PricingPage = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;
  
  const pageTitle = getTranslation('pricingPageTitle', 'Harga & Paket Fleksibel');
  const pageDescription = getTranslation('pricingPageDescription', 'Temukan solusi handling dan bundling umrah yang sesuai kebutuhan perjalanan Anda. Kami menawarkan pilihan transparan dengan nilai terbaik.');
  
  return (
    <CurrencyProvider>
      <SEO
        title={pageTitle}
        description={pageDescription}
      />
      <div className="min-h-screen flex flex-col bg-background">
        <Navbar />
        <main className="grow">
          <PricingTables />
        </main>
        <Footer />
      </div>
    </CurrencyProvider>
  );
};

export default PricingPage;
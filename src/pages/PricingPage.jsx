import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import PricingTables from '@/components/pricing/PricingTables';
import SEO from '@/components/shared/SEO.jsx';
import { LanguageContext } from '@/contexts/LanguageContext';
import { CurrencyProvider } from '@/contexts/CurrencyContext';

const PricingPage = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;
  
  const pageTitle = getTranslation('pricingPageTitle', 'Harga & Paket Fleksibel');
  const pageDescription = getTranslation('pricingPageDescription', 'Temukan solusi handling dan bundling umrah yang sesuai kebutuhan perjalanan Anda. Kami menawarkan pilihan transparan dengan nilai terbaik.');
  const pageKeywords = "harga handling umrah, paket land arrangement, biaya visa umrah, harga paket umrah b2b, pricelist umrah";

  const pricingSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": pageTitle,
    "description": pageDescription,
    "url": "https://www.umrahservice.co/pricing",
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "item": {
            "@type": "Offer",
            "name": "Paket Handling Service",
            "description": "Layanan handling profesional di bandara dan hotel di Arab Saudi."
          }
        },
        {
          "@type": "ListItem",
          "position": 2,
          "item": {
            "@type": "Offer",
            "name": "Paket Bundling Visa + Handling",
            "description": "Solusi lengkap pengurusan visa dan layanan handling."
          }
        },
        {
          "@type": "ListItem",
          "position": 3,
          "item": {
            "@type": "Offer",
            "name": "Paket LA B2B",
            "description": "Paket Land Arrangement komprehensif untuk mitra travel agent."
          }
        }
      ]
    }
  };
  
  return (
    <CurrencyProvider>
      <SEO
        title={pageTitle}
        description={pageDescription}
        keywords={pageKeywords}
        schema={pricingSchema}
      />
      <div className="min-h-screen flex flex-col bg-background">
        <Navbar />
        <main className="grow">
          <PricingTables />
        </main>
        <Footer />
      </div>
    </CurrencyProvider>
  );
};

export default PricingPage;
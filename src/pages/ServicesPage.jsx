import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ServicesList from '@/components/services/ServicesList';
import CallToAction from '@/components/home/<USER>';
import SEO from '@/components/shared/SEO.jsx';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const ServicesPage = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const pageTitle = getTranslation('servicesPageTitle', 'Layanan Komprehensif Umrah & Haji');
  const pageDescription = getTranslation('servicesPageSubtitle', 'Jelajahi layanan lengkap kami: handling B2B, visa, akomodasi, transportasi, katering, dan paket tour ziarah untuk travel agent dan jamaah mandiri.');
  const pageKeywords = "layanan umrah, layanan haji, handling b2b, visa umrah, akomo<PERSON>i makkah, transportasi jeddah, katering umrah, ziarah makkah madinah";

  const servicesSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "serviceType": "Layanan Umrah dan Haji",
    "provider": {
      "@type": "Organization",
      "name": "Arrahmah Handling Service"
    },
    "description": pageDescription,
    "name": pageTitle
  };

  return (
    <>
      <SEO
        title={pageTitle}
        description={pageDescription}
        keywords={pageKeywords}
        schema={servicesSchema}
      />
      <div className="min-h-screen flex flex-col bg-background">
        <Navbar />
        <main className="grow pt-24">
          <ServicesList />
          <CallToAction />
        </main>
        <Footer />
      </div>
    </>
  );
};

export default ServicesPage;
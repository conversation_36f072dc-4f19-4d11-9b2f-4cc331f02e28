import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { useTestimonials } from '@/contexts/TestimonialsContext.jsx';
import TestimonialCard from '@/components/testimonials/TestimonialCard.jsx';
import SEO from '@/components/shared/SEO.jsx';
import { motion } from 'framer-motion';
import { MessageSquare } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const TestimonialsPage = () => {
  const { testimonials, isLoading } = useTestimonials();
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const pageTitle = getTranslation('testimonialsPageTitle', 'Apa Kata Mereka?');
  const pageDescription = getTranslation('testimonialsPageSubtitle', 'Kami bangga dapat memberikan layanan terbaik. Berikut adalah testimoni dari beberapa mitra dan jamaah yang telah mempercayakan perjalanannya kepada kami.');
  const pageKeywords = "testimoni umrah, review layanan handling, ulasan travel agent, pengalaman jamaah, testimoni arrahmah";

  const testimonialsSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": pageTitle,
    "description": pageDescription,
    "url": "https://www.umrahservice.co/testimonials",
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": testimonials.map((testimonial, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": testimonial.rating
          },
          "author": {
            "@type": "Person",
            "name": testimonial.name
          },
          "reviewBody": testimonial.testimonial
        }
      }))
    }
  };

  return (
    <>
      <SEO
        title={pageTitle}
        description={pageDescription}
        keywords={pageKeywords}
        schema={testimonialsSchema}
      />
      <div className="flex flex-col min-h-screen bg-background">
        <Navbar />
        <main className="grow">
          <section className="py-24 bg-linear-to-b from-background to-secondary">
            <div className="container mx-auto px-4 md:px-6">
              <motion.div
                initial={{ opacity: 0, y: -30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, type: 'spring' }}
                className="text-center mb-16"
              >
                <MessageSquare className="w-16 h-16 mx-auto mb-6 text-[#FFD700]" />
                <h1 className="gradient-text mb-6 text-5xl md:text-6xl font-extrabold tracking-tight">
                  {pageTitle}
                </h1>
                <p className="text-gray-400 max-w-3xl mx-auto text-lg md:text-xl">
                  {pageDescription}
                </p>
              </motion.div>

              {isLoading ? (
                <p className="text-center text-white">{getTranslation('loadingTestimonials', 'Memuat testimoni...')}</p>
              ) : testimonials && testimonials.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {testimonials.map((testimonial, index) => (
                    <TestimonialCard key={testimonial.id} testimonial={testimonial} index={index} />
                  ))}
                </div>
              ) : (
                 <p className="text-center text-gray-400">{getTranslation('noTestimonialsAvailable', 'Saat ini belum ada testimoni yang tersedia.')}</p>
              )}
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default TestimonialsPage;
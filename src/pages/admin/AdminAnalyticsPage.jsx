import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { BarChart3, Download, Users, FileText, Newspaper, ExternalLink, Package, MessageCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { useBlog } from '@/contexts/BlogContext.jsx';
import { leadsStorageService } from '@/services/leadsStorageService.js';
import { orderStorageService } from '@/services/orderStorageService.js';

const AdminAnalyticsPage = () => {
  const { toast } = useToast();
  const { originalPosts } = useBlog();
  const [stats, setStats] = useState({
    blogPosts: 0,
    leads: 0,
    orders: 0,
    totalSubmissions: 0,
  });

  const fetchStats = useCallback(() => {
    const allLeads = leadsStorageService.loadAllLeads();
    const allOrders = orderStorageService.getOrders();
    
    setStats({
        blogPosts: originalPosts.length,
        leads: allLeads.length,
        orders: allOrders.length,
        totalSubmissions: allLeads.length + allOrders.length,
    });
  }, [originalPosts]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const handleExportReport = (format) => {
    toast({
      title: `🚧 Export Laporan ${format.toUpperCase()}`,
      description: `Fungsionalitas untuk export laporan ke ${format.toUpperCase()} belum diimplementasikan.`,
    });
  };

  const analyticsData = [
    { title: 'Total Formulir Terkirim', value: stats.totalSubmissions, icon: <FileText className="h-6 w-6 text-primary" /> },
    { title: 'Total Leads Masuk', value: stats.leads, icon: <Users className="h-6 w-6 text-primary" /> },
    { title: 'Total Order Masuk', value: stats.orders, icon: <Package className="h-6 w-6 text-primary" /> },
    { title: 'Total Post Blog', value: stats.blogPosts, icon: <Newspaper className="h-6 w-6 text-primary" /> },
  ];

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: "easeOut"
      }
    })
  };

  return (
    <div className="space-y-6">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <h1 className="text-3xl font-bold text-white tracking-tight">Analytics & Laporan</h1>
        <div className="flex space-x-2">
          <Button onClick={() => handleExportReport('csv')} variant="outline" className="text-gray-300 border-gray-600 hover:bg-gray-700 hover:text-white">
            <Download size={20} className="mr-2" /> Export CSV
          </Button>
          <Button onClick={() => handleExportReport('pdf')} className="gold-gradient text-black font-semibold hover:opacity-90">
            <Download size={20} className="mr-2" /> Export PDF
          </Button>
        </div>
      </motion.div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {analyticsData.map((item, index) => (
          <motion.div key={item.title} custom={index} variants={cardVariants} initial="hidden" animate="visible">
            <Card className="bg-gray-800 border-gray-700 text-white shadow-lg hover:shadow-primary/30 transition-shadow duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-300">{item.title}</CardTitle>
                {item.icon}
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{item.value}</div>
                {item.description && <p className="text-xs text-gray-400 mt-1">{item.description}</p>}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
      
      <motion.div custom={analyticsData.length} variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center">
              <BarChart3 className="h-6 w-6 mr-2 text-primary" /> Grafik Pengunjung Bulanan
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[300px] flex flex-col items-center justify-center text-center space-y-4">
            <BarChart3 className="h-20 w-20 text-green-500 mb-4" />
            <h3 className="text-xl font-semibold text-white">Integrasi Google Analytics Diperlukan</h3>
            <p className="text-gray-400 max-w-md">Untuk menampilkan data trafik pengunjung dan klik tombol, Anda perlu menghubungkan situs ini dengan akun Google Analytics Anda.</p>
            <a href="https://analytics.google.com/" target="_blank" rel="noopener noreferrer">
              <Button className="gold-gradient text-black font-semibold hover:opacity-90 mt-2">
                <ExternalLink size={20} className="mr-2" /> Buka Dasbor Google Analytics
              </Button>
            </a>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default AdminAnalyticsPage;
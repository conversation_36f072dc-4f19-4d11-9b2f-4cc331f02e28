import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button.jsx';
import { Plus, Settings } from 'lucide-react';
import { useBlog } from '@/contexts/BlogContext.jsx';
import { useToast } from '@/components/ui/use-toast.js';
import PreviewPostDialog from '@/components/admin/PreviewPostDialog.jsx';
import BlogStatistics from '@/components/admin/blog/BlogStatistics.jsx';
import BlogSettings from '@/components/admin/blog/BlogSettings.jsx';
import BlogFilters from '@/components/admin/blog/BlogFilters.jsx';
import BlogPostList from '@/components/admin/blog/BlogPostList.jsx';

const AdminBlogPage = () => {
  const { originalPosts, posts: translatedPosts, deletePost, isLoading, blogSettings, updateBlogSettings } = useBlog();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [previewPost, setPreviewPost] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  const [tempSettings, setTempSettings] = useState(blogSettings);

  const filteredPosts = useMemo(() => {
    return originalPosts.filter(post => {
      const matchesSearch = post.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.content?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || post.status === statusFilter;
      const matchesCategory = categoryFilter === 'all' || !post.category || post.category === categoryFilter;
      return matchesSearch && matchesStatus && matchesCategory;
    }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }, [originalPosts, searchTerm, statusFilter, categoryFilter]);

  const categories = useMemo(() => {
    return ['all', ...new Set(originalPosts.map(p => p.category).filter(Boolean))];
  }, [originalPosts]);

  const stats = useMemo(() => {
    const published = originalPosts.filter(p => p.status === 'Published').length;
    const draft = originalPosts.filter(p => p.status === 'Draft').length;
    const totalViews = originalPosts.reduce((sum, p) => sum + (p.views || 0), 0);
    return { total: originalPosts.length, published, draft, totalViews };
  }, [originalPosts]);

  const handleDeletePost = (postId) => {
    deletePost(postId);
    toast({
      title: "Post Dihapus",
      description: "Post berhasil dihapus dari blog.",
    });
  };
  
  const handlePreviewPost = (post) => {
    const translated = translatedPosts.find(p => p.id === post.id);
    setPreviewPost(translated || post);
  };

  const handleSettingsChange = (newSettings) => {
    setTempSettings(prev => ({ ...prev, ...newSettings }));
  };

  const handleSaveSettings = () => {
    updateBlogSettings(tempSettings);
    setShowSettings(false);
    toast({
      title: "Pengaturan Disimpan",
      description: "Pengaturan blog berhasil diperbarui.",
    });
  };

  const handleCancelSettings = () => {
    setTempSettings(blogSettings);
    setShowSettings(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Manajemen Blog</h1>
          <p className="text-gray-400 mt-2">Kelola artikel dan konten blog Anda</p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={() => setShowSettings(!showSettings)}
            variant="outline"
            className="text-primary border-primary hover:bg-primary/10"
          >
            <Settings size={18} className="mr-2" />
            Pengaturan Blog
          </Button>
          <Button asChild className="bg-primary text-black hover:bg-primary/90">
            <Link to="/admin/post-editor">
              <Plus size={18} className="mr-2" />
              Buat Post Baru
            </Link>
          </Button>
        </div>
      </div>

      <BlogStatistics stats={stats} />

      {showSettings && (
        <BlogSettings
          settings={tempSettings}
          onSettingsChange={handleSettingsChange}
          onSave={handleSaveSettings}
          onCancel={handleCancelSettings}
        />
      )}

      <BlogFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        statusFilter={statusFilter}
        onStatusChange={setStatusFilter}
        categoryFilter={categoryFilter}
        onCategoryChange={setCategoryFilter}
        categories={categories}
        totalPosts={originalPosts.length}
        filteredCount={filteredPosts.length}
      />

      <BlogPostList
        posts={filteredPosts}
        isLoading={isLoading}
        onDeletePost={handleDeletePost}
        onPreviewPost={handlePreviewPost}
      />

      {previewPost && (
        <PreviewPostDialog
          isOpen={!!previewPost}
          onClose={() => setPreviewPost(null)}
          postData={previewPost}
        />
      )}
    </div>
  );
};

export default AdminBlogPage;
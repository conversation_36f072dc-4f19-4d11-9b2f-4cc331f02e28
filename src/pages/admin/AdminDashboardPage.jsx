import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Newspaper, LayoutDashboard, BarChart, PlusCircle, Edit, Users, Package, Eye } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthenticationContext.jsx';
import { useBlog } from '@/contexts/BlogContext.jsx';
import { useContent } from '@/contexts/ContentContext.jsx';
import { leadsStorageService } from '@/services/leadsStorageService.js';
import { orderStorageService } from '@/services/orderStorageService.js';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

const StatCard = ({ title, value, icon, description, colorClass }) => (
  <motion.div
    whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0,0,0,0.2)" }}
    className="h-full"
  >
    <Card className={`bg-gray-800 border-gray-700 text-white h-full flex flex-col`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className={`${colorClass} p-2 rounded-lg`}>
          {icon}
        </div>
      </CardHeader>
      <CardContent className="grow">
        <div className="text-4xl font-bold">{value}</div>
        <p className="text-xs text-gray-400 mt-1">{description}</p>
      </CardContent>
    </Card>
  </motion.div>
);

const ActionCard = ({ title, description, icon, linkTo, buttonText }) => (
    <motion.div 
      className="h-full"
      whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0,0,0,0.2)" }}
    >
        <Card className="bg-gray-800 border-gray-700 text-white h-full flex flex-col justify-between p-6">
            <div>
                <div className="flex items-center space-x-4 mb-4">
                    <div className="p-3 bg-primary/10 rounded-lg">
                        {icon}
                    </div>
                    <div>
                        <h3 className="text-xl font-bold">{title}</h3>
                        <p className="text-gray-400 text-sm">{description}</p>
                    </div>
                </div>
            </div>
            <Link to={linkTo}>
                <Button className="w-full gold-gradient text-black font-semibold mt-4 hover:opacity-90">
                    {buttonText}
                </Button>
            </Link>
        </Card>
    </motion.div>
);


const AdminDashboardPage = () => {
  const { user } = useAuth();
  const { originalPosts } = useBlog();
  const { partners } = useContent();
  const [stats, setStats] = useState({
    totalPosts: 0,
    publishedPosts: 0,
    totalLeads: 0,
    totalOrders: 0,
    totalPartners: 0,
  });
  
  const [recentPosts, setRecentPosts] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);

  useEffect(() => {
    try {
      const publishedPosts = originalPosts.filter(p => p.status === 'Published').length;
      
      const allLeads = leadsStorageService.loadAllLeads();
      const allOrders = orderStorageService.getOrders();

      setStats({
        totalPosts: originalPosts.length,
        publishedPosts,
        totalLeads: allLeads.length,
        totalOrders: allOrders.length,
        totalPartners: partners.length,
      });

      const sortedPosts = [...originalPosts].sort((a,b) => new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt));
      setRecentPosts(sortedPosts.slice(0, 3));

      const sortedOrders = [...allOrders].sort((a,b) => new Date(b.createdAt) - new Date(a.createdAt));
      setRecentOrders(sortedOrders.slice(0, 3));
      
    } catch(error) {
      console.error("Failed to load dashboard stats from localStorage", error);
    }
  }, [originalPosts, partners]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('id-ID', { day: '2-digit', month: 'short', year: 'numeric' });
  }

  return (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold text-white">Dashboard</h1>
        <p className="text-gray-400 mt-1">Selamat datang kembali, {user?.name || 'Admin'}! Berikut ringkasan situs Anda.</p>
      </motion.div>
      
      <motion.div 
        className="grid gap-6 md:grid-cols-2 lg:grid-cols-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <StatCard 
            title="Total Order Masuk" 
            value={stats.totalOrders} 
            icon={<Package size={22} className="text-orange-400" />}
            description="Dari semua formulir order"
            colorClass="bg-orange-400/10"
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <StatCard 
            title="Total Leads" 
            value={stats.totalLeads} 
            icon={<Users size={22} className="text-green-400" />}
            description="Dari form kontak & janji temu"
            colorClass="bg-green-400/10"
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <StatCard 
            title="Total Postingan" 
            value={stats.totalPosts} 
            icon={<Newspaper size={22} className="text-blue-400" />}
            description={`${stats.publishedPosts} post dipublikasikan`}
            colorClass="bg-blue-400/10"
          />
        </motion.div>
         <motion.div variants={itemVariants}>
          <StatCard 
            title="Total Mitra" 
            value={stats.totalPartners} 
            icon={<Users size={22} className="text-purple-400" />}
            description="Mitra travel yang terdaftar"
            colorClass="bg-purple-400/10"
          />
        </motion.div>
      </motion.div>

      <motion.div 
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants} className="lg:col-span-2 space-y-6">
            <Card className="bg-gray-800 border-gray-700 text-white h-full">
                <CardHeader>
                    <CardTitle>Order Masuk Terbaru</CardTitle>
                    <CardDescription>3 order terakhir yang masuk.</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {recentOrders.length > 0 ? recentOrders.map(order => (
                            <div key={order.id} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-700/50 transition-colors">
                                <div>
                                    <p className="font-semibold text-white">{order.formData.bookerInfo.travelName}</p>
                                    <p className="text-sm text-gray-400">
                                        {order.summary.packageName} - ${order.summary.total.toFixed(2)}
                                    </p>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-gray-400">{formatDate(order.createdAt)}</span>
                                  <Link to={`/admin/orders`}>
                                      <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-primary">
                                          <Eye size={16} />
                                      </Button>
                                  </Link>
                                </div>
                            </div>
                        )) : (
                          <p className="text-gray-400 text-center py-8">Belum ada order masuk.</p>
                        )}
                    </div>
                </CardContent>
            </Card>
            <Card className="bg-gray-800 border-gray-700 text-white h-full">
                <CardHeader>
                    <CardTitle>Aktivitas Blog Terbaru</CardTitle>
                    <CardDescription>3 post terakhir yang diperbarui atau dibuat.</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {recentPosts.length > 0 ? recentPosts.map(post => (
                            <div key={post.id} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-700/50 transition-colors">
                                <div>
                                    <p className="font-semibold text-white">{post.title}</p>
                                    <p className="text-sm text-gray-400">
                                        Diperbarui pada {formatDate(post.updatedAt || post.createdAt)}
                                    </p>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <span className={`px-2 py-0.5 rounded-full text-xs font-medium
                                    ${post.status === 'Published' ? 'bg-green-500/20 text-green-400' : 
                                      post.status === 'Draft' ? 'bg-yellow-500/20 text-yellow-400' : 
                                      'bg-gray-500/20 text-gray-400'}`}>
                                    {post.status}
                                  </span>
                                  <Link to={`/admin/post-editor/${post.id}`}>
                                      <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-primary">
                                          <Edit size={16} />
                                      </Button>
                                  </Link>
                                </div>
                            </div>
                        )) : (
                          <p className="text-gray-400 text-center py-8">Belum ada aktivitas blog.</p>
                        )}
                    </div>
                </CardContent>
            </Card>
        </motion.div>
        
        <motion.div variants={itemVariants} className="space-y-6">
            <ActionCard 
                title="Manajemen Order"
                description="Kelola semua pesanan masuk."
                icon={<Package size={24} className="text-primary"/>}
                linkTo="/admin/orders"
                buttonText="Kelola Order"
            />
             <ActionCard 
                title="Tulis Postingan Baru"
                description="Buat konten baru yang menarik."
                icon={<PlusCircle size={24} className="text-primary"/>}
                linkTo="/admin/post-editor"
                buttonText="Tulis Sekarang"
            />
        </motion.div>
      </motion.div>

    </div>
  );
};

export default AdminDashboardPage;
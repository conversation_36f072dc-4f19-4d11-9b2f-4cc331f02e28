import React, { useState } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { LayoutDashboard, Newspaper, FileText, Image as ImageIcon, Users, BarChart2, Settings, UserCircle, LogOut, ChevronLeft, ChevronRight, Menu, Home, HeartHandshake as Handshake, Languages, ExternalLink, Package as PackageIcon, Send, Star } from 'lucide-react';
import { Button } from '@/components/ui/button.jsx';
import { useToast } from '@/components/ui/use-toast.js';
import { useAuth } from '@/contexts/AuthenticationContext.jsx';

const AdminLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, logout } = useAuth();

  const logoUrl = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e2226538273674d2415bfb7f2ef1cba1.png";


  const navItems = [
    { name: 'Dashboard', icon: <LayoutDashboard size={20} />, path: '/admin/dashboard' },
    { name: 'Manajemen Order', icon: <PackageIcon size={20} />, path: '/admin/orders' },
    { name: 'Editor Beranda', icon: <Home size={20} />, path: '/admin/home-editor'},
    { name: 'Blog', icon: <Newspaper size={20} />, path: '/admin/blog' },
    { name: 'Konten Utama', icon: <FileText size={20} />, path: '/admin/content' },
    { name: 'Paket Harga', icon: <PackageIcon size={20} />, path: '/admin/pricing-packages' },
    { name: 'Mitra', icon: <Handshake size={20} />, path: '/admin/partners' },
    { name: 'Testimoni', icon: <Star size={20} />, path: '/admin/testimonials' },
    { name: 'Marketing Booster', icon: <Send size={20} />, path: '/admin/marketing-booster' },
    { name: 'Media', icon: <ImageIcon size={20} />, path: '/admin/media' },
    { name: 'Leads', icon: <Users size={20} />, path: '/admin/leads' },
    { name: 'Analytics', icon: <BarChart2 size={20} />, path: '/admin/analytics' },
    { name: 'Pengaturan Situs', icon: <Settings size={20} />, path: '/admin/settings' },
    { name: 'Pengaturan Bahasa', icon: <Languages size={20} />, path: '/admin/language-settings' },
    { name: 'Manajemen Admin', icon: <UserCircle size={20} />, path: '/admin/users' },
  ];

  const isActive = (path) => location.pathname.startsWith(path);

  const handleLogout = () => {
    logout();
    toast({
      title: "Logout Berhasil",
      description: "Anda telah berhasil keluar.",
      variant: "default",
    });
    navigate('/admin/login');
  };
  
  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      <div className="p-4 flex items-center justify-between border-b border-gray-700">
        <Link to="/admin" className="flex items-center" onClick={() => mobileSidebarOpen && setMobileSidebarOpen(false)}>
          <img src={logoUrl} alt="Arrahmah Admin Logo" className="h-10 w-auto mr-2" />
          <span className={`font-semibold text-xl text-white ${!sidebarOpen && !mobileSidebarOpen ? 'hidden' : ''}`}>Admin</span>
        </Link>
        {sidebarOpen && !mobileSidebarOpen && (
          <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(false)} className="text-gray-400 hover:text-white hidden lg:inline-flex">
            <ChevronLeft size={20} />
          </Button>
        )}
      </div>
      <nav className="grow p-4 space-y-2 overflow-y-auto">
        {navItems.map((item) => (
          <Link
            key={item.name}
            to={item.path}
            onClick={() => mobileSidebarOpen && setMobileSidebarOpen(false)}
            className={`flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-colors duration-200
              ${isActive(item.path) ? 'bg-primary/80 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}
              ${(!sidebarOpen && !mobileSidebarOpen) ? 'justify-center' : ''}`}
          >
            {item.icon}
            <span className={`${(!sidebarOpen && !mobileSidebarOpen) ? 'hidden' : 'block'}`}>{item.name}</span>
          </Link>
        ))}
      </nav>
      <div className="p-4 border-t border-gray-700 space-y-2">
        <Link to="/" target="_blank" rel="noopener noreferrer"
          className={`w-full flex items-center space-x-3 text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2.5 rounded-lg transition-colors duration-200
            ${(!sidebarOpen && !mobileSidebarOpen) ? 'justify-center' : ''}`}
        >
          <ExternalLink size={20} />
          <span className={`${(!sidebarOpen && !mobileSidebarOpen) ? 'hidden' : 'block'}`}>Lihat Situs</span>
        </Link>
        <Button 
          variant="ghost" 
          onClick={handleLogout}
          className={`w-full flex items-center space-x-3 text-gray-300 hover:bg-red-500/20 hover:text-red-400
            ${(!sidebarOpen && !mobileSidebarOpen) ? 'justify-center' : ''}`}
        >
          <LogOut size={20} />
          <span className={`${(!sidebarOpen && !mobileSidebarOpen) ? 'hidden' : 'block'}`}>Logout</span>
        </Button>
      </div>
    </div>
  );


  return (
    <div className="flex h-screen bg-gray-900 text-white">
      {/* Desktop Sidebar */}
      <aside 
        className={`hidden lg:flex flex-col bg-gray-800 transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'w-64' : 'w-20'}`}
      >
        <SidebarContent />
      </aside>

      {/* Mobile Sidebar */}
      {mobileSidebarOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/50 lg:hidden" 
          onClick={() => setMobileSidebarOpen(false)}
        ></div>
      )}
      <aside 
        className={`fixed inset-y-0 left-0 z-40 flex flex-col bg-gray-800 transition-transform duration-300 ease-in-out lg:hidden
        ${mobileSidebarOpen ? 'translate-x-0 w-64' : '-translate-x-full w-64'}`}
      >
         <SidebarContent />
      </aside>


      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-gray-800 shadow-md lg:shadow-none">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <button 
                  onClick={() => setSidebarOpen(!sidebarOpen)} 
                  className="hidden lg:inline-flex p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-hidden focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  {sidebarOpen ? <ChevronLeft size={24} /> : <ChevronRight size={24} />}
                </button>
                <button 
                  onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)} 
                  className="lg:hidden p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-hidden focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  <Menu size={24} />
                </button>
              </div>
              <div className="flex items-center">
                <span className="text-sm text-gray-300 mr-3">Selamat datang, {user?.name || 'Admin'}!</span>
                <UserCircle size={28} className="text-primary" />
              </div>
            </div>
          </div>
        </header>
        
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-900 p-4 sm:p-6 lg:p-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { useToast } from '@/components/ui/use-toast.js';
import { Eye, EyeOff, LogIn } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthenticationContext.jsx';

const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { login, isAuthenticated } = useAuth();
  const logoUrl = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e2226538273674d2415bfb7f2ef1cba1.png";

  const from = location.state?.from?.pathname || "/admin/dashboard";

  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);


  const handleSubmit = (e) => {
    e.preventDefault();
    if (!email || !password) {
      toast({
        title: "Input Tidak Lengkap",
        description: "Mohon isi email dan password.",
        variant: "destructive",
      });
      return;
    }
    setIsLoading(true);
    
    setTimeout(() => {
      try {
        const storedUsers = JSON.parse(localStorage.getItem('adminUsers') || '[]');
        const user = storedUsers.find(u => u.email === email && u.password === password);
        
        if (user) {
          const userData = { id: user.id, name: user.name, email: user.email, role: user.role };
          login(userData);
          toast({
            title: "Login Berhasil!",
            description: `Selamat datang kembali, ${user.name}.`,
            variant: "default",
          });
          navigate(from, { replace: true });
        } else {
           toast({
            title: "Login Gagal",
            description: "Email atau password salah. Silakan coba lagi.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error during login process:", error);
        toast({
          title: "Error Sistem",
          description: "Terjadi kesalahan saat mencoba login.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-linear-to-br from-gray-900 via-gray-800 to-secondary p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <Card className="w-full max-w-md bg-gray-800 border-gray-700 text-white shadow-2xl">
          <CardHeader className="text-center">
            <img src={logoUrl} alt="Arrahmah Service Logo" className="w-32 mx-auto mb-4" />
            <CardTitle className="text-3xl font-bold tracking-tight bg-clip-text text-transparent gold-gradient-text-subtle">
              Admin Login
            </CardTitle>
            <CardDescription className="text-gray-400">
              Masuk untuk mengelola konten Umrahservice.co
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-300">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-500 focus:border-primary"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-300">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="bg-gray-700 border-gray-600 text-white placeholder-gray-500 focus:border-primary pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 text-gray-400 hover:text-primary"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </Button>
                </div>
              </div>
              <Button 
                type="submit" 
                className="w-full gold-gradient text-black font-semibold hover:opacity-90 transition-opacity duration-200 flex items-center justify-center"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
                ) : (
                  <>
                    <LogIn size={18} className="mr-2" /> Login
                  </>
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col items-center space-y-2">
            <Button variant="link" className="text-sm text-primary/80 hover:text-primary">
              Lupa password?
            </Button>
            <p className="text-xs text-gray-500">
              &copy; {new Date().getFullYear()} Arrahmah Handling Service.
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default AdminLoginPage;
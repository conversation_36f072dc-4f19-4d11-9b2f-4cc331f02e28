import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter as DialogPrimitiveFooter } from "@/components/ui/dialog.jsx";
import { UploadCloud, Image as ImageIcon, Trash2, Eye, Info, Search, FileText } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { Textarea } from '@/components/ui/textarea.jsx';

const AdminMediaPage = () => {
  const { toast } = useToast();
  const [mediaItems, setMediaItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);

  useEffect(() => {
    const storedMedia = localStorage.getItem('adminMediaItems');
    if (storedMedia) {
      setMediaItems(JSON.parse(storedMedia));
    } else {
      // Initialize with some placeholder if needed, or leave empty
      const placeholderMedia = [
        { id: 'placeholder1', name: 'Logo Arrahmah.png', type: 'image/png', size: 12045, dataUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e2226538273674d2415bfb7f2ef1cba1.png', uploadDate: new Date().toISOString() },
        { id: 'placeholder2', name: 'Favicon.svg', type: 'image/svg+xml', size: 1500, dataUrl: '/favicon.svg', uploadDate: new Date().toISOString() },
      ];
      setMediaItems(placeholderMedia);
      localStorage.setItem('adminMediaItems', JSON.stringify(placeholderMedia));
    }
  }, []);

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    const newMediaItems = [];
    files.forEach(file => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const newItem = {
          id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
          name: file.name,
          type: file.type,
          size: file.size,
          dataUrl: reader.result,
          uploadDate: new Date().toISOString(),
        };
        newMediaItems.push(newItem);
        
        if (newMediaItems.length === files.length) {
          const updatedMedia = [...mediaItems, ...newMediaItems];
          setMediaItems(updatedMedia);
          localStorage.setItem('adminMediaItems', JSON.stringify(updatedMedia));
          toast({ title: `${files.length} Media Diunggah`, description: "File berhasil ditambahkan ke pustaka media." });
        }
      };
      reader.onerror = () => {
        toast({ title: "Gagal Membaca File", description: `Tidak dapat membaca file ${file.name}.`, variant: "destructive" });
      };
      reader.readAsDataURL(file);
    });
    event.target.value = null; // Reset file input
  };

  const handleDeleteMedia = (mediaId) => {
    const updatedMedia = mediaItems.filter(item => item.id !== mediaId);
    setMediaItems(updatedMedia);
    localStorage.setItem('adminMediaItems', JSON.stringify(updatedMedia));
    toast({ title: "Media Dihapus", description: "File media telah dihapus dari pustaka." });
    if (selectedMedia && selectedMedia.id === mediaId) {
      setIsViewModalOpen(false);
      setSelectedMedia(null);
    }
  };

  const handleViewMedia = (mediaItem) => {
    setSelectedMedia(mediaItem);
    setIsViewModalOpen(true);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredMedia = mediaItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  ).sort((a,b) => new Date(b.uploadDate) - new Date(a.uploadDate));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.05 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } }
  };

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <h1 className="text-3xl font-bold text-white tracking-tight flex items-center">
          <ImageIcon size={30} className="mr-3 text-primary" /> Pustaka Media
        </h1>
        <Label htmlFor="mediaUpload" className="gold-gradient text-black font-semibold hover:opacity-90 px-4 py-2 rounded-md cursor-pointer inline-flex items-center">
          <UploadCloud size={20} className="mr-2" /> Unggah Media Baru
        </Label>
        <Input id="mediaUpload" type="file" multiple accept="image/*,video/*" onChange={handleFileUpload} className="hidden" />
      </motion.div>

      <motion.div variants={itemVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
          <div className="relative mb-6">
            <Input
              type="text"
              placeholder="Cari nama file media..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>

          {filteredMedia.length === 0 ? (
            <motion.p variants={itemVariants} className="text-center text-gray-400 py-8">
              Tidak ada media ditemukan atau pustaka masih kosong.
            </motion.p>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
            >
              {filteredMedia.map(item => (
                <motion.div key={item.id} variants={itemVariants}>
                  <Card className="bg-gray-700/70 border-gray-600 text-white shadow-md hover:shadow-primary/20 transition-shadow duration-300 group aspect-square flex flex-col">
                    <CardContent className="p-2 grow flex items-center justify-center overflow-hidden">
                      {item.type.startsWith('image/') ? (
                        <img src={item.dataUrl} alt={item.name} className="max-h-full max-w-full object-contain rounded-sm" />
                      ) : (
                        <div className="text-center">
                          <FileText size={32} className="mx-auto text-gray-400" />
                          <p className="text-xs text-gray-500 mt-1">Preview tidak tersedia</p>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="p-2 border-t border-gray-600/50 text-xs">
                      <div className="w-full">
                        <p className="font-medium text-primary truncate" title={item.name}>{item.name}</p>
                        <p className="text-gray-400">{formatFileSize(item.size)}</p>
                        <div className="flex justify-end space-x-1 mt-1">
                          <Button variant="ghost" size="icon" onClick={() => handleViewMedia(item)} className="h-6 w-6 text-gray-400 hover:text-primary"><Eye size={14} /></Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDeleteMedia(item.id)} className="h-6 w-6 text-gray-400 hover:text-red-500"><Trash2 size={14} /></Button>
                        </div>
                      </div>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}
        </Card>
      </motion.div>

      {selectedMedia && (
        <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
          <DialogContent className="bg-gray-800 border-gray-700 text-white sm:max-w-lg">
            <DialogHeader>
              <DialogTitle className="text-primary text-xl truncate" title={selectedMedia.name}>{selectedMedia.name}</DialogTitle>
              <DialogDescription className="text-gray-400">Detail File Media</DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-3">
              <div className="max-h-[60vh] overflow-auto flex justify-center items-center bg-black/20 rounded p-2">
                {selectedMedia.type.startsWith('image/') ? (
                  <img src={selectedMedia.dataUrl} alt={selectedMedia.name} className="max-h-full max-w-full object-contain" />
                ) : (
                  <p className="text-gray-400">Preview tidak tersedia untuk tipe file ini.</p>
                )}
              </div>
              <div className="text-sm">
                <p><strong className="text-gray-300">Nama File:</strong> {selectedMedia.name}</p>
                <p><strong className="text-gray-300">Tipe:</strong> {selectedMedia.type}</p>
                <p><strong className="text-gray-300">Ukuran:</strong> {formatFileSize(selectedMedia.size)}</p>
                <p><strong className="text-gray-300">Tanggal Unggah:</strong> {new Date(selectedMedia.uploadDate).toLocaleString('id-ID')}</p>
                <div className="mt-2">
                  <Label htmlFor="mediaUrl" className="text-gray-300">URL (Data URL - hanya untuk preview lokal)</Label>
                  <Textarea id="mediaUrl" readOnly value={selectedMedia.dataUrl} rows={3} className="bg-gray-700 border-gray-600 text-xs mt-1" />
                </div>
              </div>
            </div>
            <DialogPrimitiveFooter>
              <Button variant="outline" onClick={() => setIsViewModalOpen(false)} className="text-gray-300 border-gray-600">Tutup</Button>
              <Button variant="destructiveOutline" onClick={() => handleDeleteMedia(selectedMedia.id)} className="text-red-400 border-red-400 hover:bg-red-400/10 hover:text-red-400">
                <Trash2 size={16} className="mr-2" /> Hapus Media Ini
              </Button>
            </DialogPrimitiveFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default AdminMediaPage;
import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { orderStorageService } from '@/services/orderStorageService';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Package, User, DollarSign, Info } from 'lucide-react';
import { motion } from 'framer-motion';
import OrderStatusBadge from '@/components/admin/orders/OrderStatusBadge';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

const AdminOrderDetailPage = () => {
    const { orderId } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [order, setOrder] = useState(null);
    const [editableOrder, setEditableOrder] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    const fetchOrder = useCallback(() => {
        setIsLoading(true);
        try {
            const fetchedOrder = orderStorageService.getOrderById(orderId);
            if (fetchedOrder) {
                setOrder(fetchedOrder);
                setEditableOrder(JSON.parse(JSON.stringify(fetchedOrder))); // Deep copy for editing
            } else {
                toast({
                    title: "Order Tidak Ditemukan",
                    description: `Order dengan ID ${orderId} tidak ada.`,
                    variant: "destructive",
                });
                navigate('/admin/orders');
            }
        } catch (error) {
            toast({
                title: "Gagal Memuat Order",
                description: "Terjadi kesalahan saat mengambil data order.",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    }, [orderId, navigate, toast]);

    useEffect(() => {
        fetchOrder();
    }, [fetchOrder]);

    const handleInputChange = (section, field, value) => {
        setEditableOrder(prev => ({
            ...prev,
            formData: {
                ...prev.formData,
                [section]: {
                    ...prev.formData[section],
                    [field]: value,
                },
            },
        }));
    };

    const handleSaveChanges = () => {
        try {
            orderStorageService.updateOrder(orderId, editableOrder);
            toast({
                title: "Perubahan Disimpan",
                description: "Data order telah berhasil diperbarui.",
            });
            fetchOrder(); // Re-fetch to confirm changes
        } catch (error) {
            toast({
                title: "Gagal Menyimpan Perubahan",
                description: error.message,
                variant: "destructive",
            });
        }
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (!order) {
        return (
            <div className="text-center text-gray-400 py-16">
                <h2 className="text-2xl font-bold">Order tidak ditemukan.</h2>
                <Button onClick={() => navigate('/admin/orders')} className="mt-4">Kembali ke Daftar Order</Button>
            </div>
        );
    }

    const { formData, summary, status, createdAt } = editableOrder;

    const packageTypeLabels = {
        handling_service: 'Paket Handling Service',
        bundling_visa_handling: 'Paket Bundling Visa + Handling',
        la_b2b: 'Paket LA B2B',
        handling_airport_only: 'Paket Handling Airport Only',
        custom_request: 'Custom Request',
    };

    return (
        <div className="space-y-6">
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                <div className="flex justify-between items-center mb-4">
                    <Button variant="outline" onClick={() => navigate('/admin/orders')} className="text-gray-300 border-gray-600 hover:bg-gray-700">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Kembali
                    </Button>
                    <Button onClick={handleSaveChanges} className="gold-gradient text-black font-semibold hover:opacity-90">
                        <Save className="mr-2 h-4 w-4" />
                        Simpan Perubahan
                    </Button>
                </div>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-white tracking-tight">Detail Order: <span className="text-amber-400">{order.id}</span></h1>
                        <p className="text-gray-400 mt-1">
                            Dipesan pada: {format(new Date(createdAt), "d MMMM yyyy, HH:mm", { locale: id })}
                        </p>
                    </div>
                    <OrderStatusBadge status={status} />
                </div>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                    <Card className="bg-gray-800 border-gray-700 text-white">
                        <CardHeader className="flex flex-row items-center space-x-4">
                            <User className="h-6 w-6 text-amber-400" />
                            <div>
                                <CardTitle>Informasi Pemesan</CardTitle>
                                <CardDescription className="text-gray-400">Detail kontak dan travel agent.</CardDescription>
                            </div>
                        </CardHeader>
                        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="travelName">Nama Travel</Label>
                                <Input id="travelName" value={formData.bookerInfo.travelName} onChange={(e) => handleInputChange('bookerInfo', 'travelName', e.target.value)} className="bg-gray-700 border-gray-600" />
                            </div>
                            <div>
                                <Label htmlFor="picName">Nama PIC</Label>
                                <Input id="picName" value={formData.bookerInfo.picName} onChange={(e) => handleInputChange('bookerInfo', 'picName', e.target.value)} className="bg-gray-700 border-gray-600" />
                            </div>
                            <div>
                                <Label htmlFor="whatsapp">WhatsApp</Label>
                                <Input id="whatsapp" value={formData.bookerInfo.whatsapp} onChange={(e) => handleInputChange('bookerInfo', 'whatsapp', e.target.value)} className="bg-gray-700 border-gray-600" />
                            </div>
                            <div>
                                <Label htmlFor="email">Email</Label>
                                <Input id="email" type="email" value={formData.bookerInfo.email} onChange={(e) => handleInputChange('bookerInfo', 'email', e.target.value)} className="bg-gray-700 border-gray-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700 text-white">
                        <CardHeader className="flex flex-row items-center space-x-4">
                            <Info className="h-6 w-6 text-amber-400" />
                            <div>
                                <CardTitle>Catatan Tambahan</CardTitle>
                                <CardDescription className="text-gray-400">Catatan dari pelanggan atau untuk internal.</CardDescription>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <Textarea
                                value={formData.notes}
                                onChange={(e) => handleInputChange('notes', null, e.target.value)}
                                placeholder="Tidak ada catatan tambahan dari pelanggan."
                                className="bg-gray-700 border-gray-600 min-h-[120px]"
                            />
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <Card className="bg-gray-800 border-gray-700 text-white">
                        <CardHeader className="flex flex-row items-center space-x-4">
                            <Package className="h-6 w-6 text-amber-400" />
                            <div>
                                <CardTitle>Ringkasan Paket</CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-400">Jenis Paket</span>
                                <span className="font-semibold">{packageTypeLabels[formData.packageType]}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-400">Nama Paket</span>
                                <span className="font-semibold">{summary.packageName}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-400">Total Jamaah</span>
                                <span className="font-semibold">{formData.totalPax} orang</span>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="bg-gray-800 border-gray-700 text-white">
                        <CardHeader className="flex flex-row items-center space-x-4">
                            <DollarSign className="h-6 w-6 text-amber-400" />
                            <div>
                                <CardTitle>Rincian Biaya</CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-400">Subtotal</span>
                                <span>${summary.subtotal?.toFixed(2)}</span>
                            </div>
                            {summary.addons?.map((addon, index) => (
                                <div key={index} className="flex justify-between items-center">
                                    <span className="text-gray-400">{addon.name}</span>
                                    <span>${addon.cost.toFixed(2)}</span>
                                </div>
                            ))}
                            <hr className="border-gray-600 my-2" />
                            <div className="flex justify-between items-center text-lg font-bold text-green-400">
                                <span>Total</span>
                                <span>${summary.total?.toFixed(2)}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default AdminOrderDetailPage;
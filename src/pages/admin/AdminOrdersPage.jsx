import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Download, Search, Package, RotateCcw } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast';
import { orderStorageService } from '@/services/orderStorageService';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import OrderStatusBadge from '@/components/admin/orders/OrderStatusBadge';
import OrderActions from '@/components/admin/orders/OrderActions';

const AdminOrdersPage = () => {
    const { toast } = useToast();
    const [orders, setOrders] = useState([]);
    const [filteredOrders, setFilteredOrders] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');

    const fetchOrders = useCallback(() => {
        setIsLoading(true);
        try {
            const allOrders = orderStorageService.getOrders();
            setOrders(allOrders);
        } catch (error) {
            console.error('Error fetching orders:', error);
            toast({
                title: "Gagal Memuat Order",
                description: "Terjadi kesalahan saat memuat data order.",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    }, [toast]);

    useEffect(() => {
        fetchOrders();
    }, [fetchOrders]);

    useEffect(() => {
        let result = orders;

        if (searchTerm) {
            const lowerCaseSearch = searchTerm.toLowerCase();
            result = result.filter(order =>
                order.id.toLowerCase().includes(lowerCaseSearch) ||
                order.formData.bookerInfo.travelName.toLowerCase().includes(lowerCaseSearch) ||
                order.formData.bookerInfo.picName.toLowerCase().includes(lowerCaseSearch) ||
                order.summary.packageName.toLowerCase().includes(lowerCaseSearch)
            );
        }

        if (statusFilter !== 'all') {
            result = result.filter(order => order.status === statusFilter);
        }

        setFilteredOrders(result);
    }, [searchTerm, statusFilter, orders]);

    const handleStatusChange = (orderId, newStatus) => {
        try {
            const updatedOrder = orderStorageService.updateOrderStatus(orderId, newStatus);
            setOrders(prevOrders => prevOrders.map(o => o.id === orderId ? updatedOrder : o));
            toast({
                title: "Status Berhasil Diperbarui",
                description: `Status order ${orderId} diubah menjadi ${newStatus}.`,
            });
        } catch (error) {
            toast({
                title: "Gagal Memperbarui Status",
                description: error.message,
                variant: "destructive",
            });
        }
    };

    const handleDeleteOrder = (orderId) => {
        try {
            orderStorageService.deleteOrder(orderId);
            setOrders(prevOrders => prevOrders.filter(o => o.id !== orderId));
            toast({
                title: "Order Berhasil Dihapus",
                description: `Order dengan ID ${orderId} telah dihapus.`,
                variant: "success",
            });
        } catch (error) {
            toast({
                title: "Gagal Menghapus Order",
                description: error.message,
                variant: "destructive",
            });
        }
    };

    const handleExportCSV = () => {
        toast({
            title: "Fungsi Belum Tersedia",
            description: "🚧 Fitur ini belum diimplementasikan—tapi jangan khawatir! Anda dapat memintanya di prompt berikutnya! 🚀",
            variant: "destructive",
        });
    };

    const packageTypeLabels = {
        handling_service: 'Handling Service',
        bundling_visa_handling: 'Bundling Visa + Handling',
        la_b2b: 'LA B2B',
        handling_airport_only: 'Handling Airport Only',
        custom_request: 'Custom Request',
    };

    return (
        <div className="space-y-6">
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
            >
                <div>
                    <h1 className="text-3xl font-bold text-white tracking-tight">Manajemen Order</h1>
                    <p className="text-gray-400 mt-1 flex items-center">
                        <Package size={16} className="mr-2" />
                        Total {orders.length} order tercatat
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button onClick={fetchOrders} variant="outline" className="text-gray-300 border-gray-600 hover:bg-gray-700" disabled={isLoading}>
                        <RotateCcw size={18} className="mr-2" /> {isLoading ? 'Memuat...' : 'Muat Ulang'}
                    </Button>
                    <Button onClick={handleExportCSV} className="gold-gradient text-black font-semibold hover:opacity-90" disabled={filteredOrders.length === 0}>
                        <Download size={20} className="mr-2" /> Export ke CSV
                    </Button>
                </div>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
                <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
                    <CardHeader>
                        <CardTitle>Filter & Pencarian</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="relative">
                            <Input
                                type="text"
                                placeholder="Cari ID order, nama travel, PIC..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10"
                            />
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-primary">
                                <SelectValue placeholder="Filter Status" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 text-white border-gray-700">
                                <SelectItem value="all">Semua Status</SelectItem>
                                <SelectItem value="Baru">Baru</SelectItem>
                                <SelectItem value="Diproses">Diproses</SelectItem>
                                <SelectItem value="Selesai">Selesai</SelectItem>
                                <SelectItem value="Dibatalkan">Dibatalkan</SelectItem>
                            </SelectContent>
                        </Select>
                    </CardContent>
                </Card>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
                    <CardHeader>
                        <CardTitle>Daftar Order ({filteredOrders.length})</CardTitle>
                        <CardDescription className="text-gray-400">Kelola dan tindak lanjuti semua order yang masuk.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="text-center text-gray-400 py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                                Memuat data order...
                            </div>
                        ) : filteredOrders.length === 0 ? (
                            <p className="text-center text-gray-400 py-8">
                                {searchTerm || statusFilter !== 'all' ? 'Tidak ada order yang cocok.' : 'Belum ada order yang masuk.'}
                            </p>
                        ) : (
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-gray-700 hover:bg-gray-700/30">
                                            <TableHead className="text-gray-300">ID Order</TableHead>
                                            <TableHead className="text-gray-300">Travel</TableHead>
                                            <TableHead className="text-gray-300">Paket</TableHead>
                                            <TableHead className="text-gray-300">Tanggal</TableHead>
                                            <TableHead className="text-gray-300">Total</TableHead>
                                            <TableHead className="text-gray-300">Status</TableHead>
                                            <TableHead className="text-gray-300 text-right">Aksi</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {filteredOrders.map((order) => (
                                            <TableRow key={order.id} className="border-gray-700 hover:bg-gray-700/30">
                                                <TableCell className="font-mono text-xs text-amber-300">{order.id}</TableCell>
                                                <TableCell>
                                                    <p className="font-medium text-white">{order.formData.bookerInfo.travelName}</p>
                                                    <p className="text-sm text-gray-400">{order.formData.bookerInfo.picName}</p>
                                                </TableCell>
                                                <TableCell className="text-gray-300">{packageTypeLabels[order.formData.packageType]}</TableCell>
                                                <TableCell className="text-gray-300">{format(new Date(order.createdAt), "d MMM yyyy", { locale: id })}</TableCell>
                                                <TableCell className="font-semibold text-green-400">${order.summary.total.toFixed(2)}</TableCell>
                                                <TableCell>
                                                    <OrderStatusBadge status={order.status} />
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <OrderActions 
                                                        order={order}
                                                        onStatusChange={handleStatusChange}
                                                        onDelete={handleDeleteOrder}
                                                    />
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </motion.div>
        </div>
    );
};

export default AdminOrdersPage;
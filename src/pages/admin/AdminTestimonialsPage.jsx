import React, { useState, useRef } from 'react';
import { useTestimonials } from '@/contexts/TestimonialsContext.jsx';
import { useToast } from '@/components/ui/use-toast.js';
import { Button } from '@/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog.jsx';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog.jsx";
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { PlusCircle, Edit, Trash2, Star, ImagePlus } from 'lucide-react';
import { motion } from 'framer-motion';

const StarRatingInput = ({ rating, setRating }) => {
  const [hover, setHover] = useState(0);
  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, index) => {
        const ratingValue = index + 1;
        return (
          <motion.div
            key={index}
            whileHover={{ scale: 1.2, y: -2 }}
            whileTap={{ scale: 0.9 }}
          >
            <Star
              className="cursor-pointer transition-colors duration-200"
              color={ratingValue <= (hover || rating) ? "#FFD700" : "#6b7280"}
              fill={ratingValue <= (hover || rating) ? "#FFD700" : "transparent"}
              onClick={() => setRating(ratingValue)}
              onMouseEnter={() => setHover(ratingValue)}
              onMouseLeave={() => setHover(0)}
            />
          </motion.div>
        );
      })}
    </div>
  );
};

const AdminTestimonialsPage = () => {
  const { testimonials, addTestimonial, editTestimonial, deleteTestimonial, isLoading } = useTestimonials();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const avatarInputRef = useRef(null);

  const handleAddNew = () => {
    setCurrentTestimonial({ name: '', role: '', quote: '', stars: 5, avatar: '', avatarFile: null });
    setAvatarPreview(null);
    setIsDialogOpen(true);
  };

  const handleEdit = (testimonial) => {
    setCurrentTestimonial({ ...testimonial, avatarFile: null });
    setAvatarPreview(testimonial.avatar);
    setIsDialogOpen(true);
  };

  const handleSave = async () => {
    if (!currentTestimonial || !currentTestimonial.name || !currentTestimonial.quote) {
        toast({ title: "Nama dan kutipan wajib diisi.", variant: "destructive" });
        return;
    }

    if (currentTestimonial.id) {
      await editTestimonial(currentTestimonial);
    } else {
      await addTestimonial(currentTestimonial);
    }
    
    setIsDialogOpen(false);
    setCurrentTestimonial(null);
    setAvatarPreview(null);
  };

  const handleDelete = (id) => {
    deleteTestimonial(id);
  };
  
  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
        setCurrentTestimonial(prev => ({...prev, avatarFile: file}));
        setAvatarPreview(URL.createObjectURL(file));
    }
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 className="text-3xl font-bold text-white">Manajemen Testimoni</h1>
        <Button onClick={handleAddNew} className="gold-gradient text-black font-semibold">
          <PlusCircle className="mr-2 h-4 w-4" /> Tambah Testimoni
        </Button>
      </div>

      {isLoading ? (
        <p className="text-white text-center py-10">Memuat testimoni...</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} className="bg-gray-800 border-gray-700 text-white flex flex-col shadow-lg hover:shadow-primary/20 transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <CardTitle className="text-primary">{testimonial.name}</CardTitle>
                    <CardDescription className="text-gray-400">{testimonial.role}</CardDescription>
                  </div>
                   <img 
                    className="w-16 h-16 rounded-full border-2 border-amber-400 object-cover bg-gray-700"
                    alt={`Avatar of ${testimonial.name}`}
                    src={testimonial.avatar || `https://ui-avatars.com/api/?name=${testimonial.name}&background=0D1117&color=fff`} 
                    />
                </div>
              </CardHeader>
              <CardContent className="grow space-y-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className={`w-5 h-5 ${i < testimonial.stars ? 'text-amber-400 fill-amber-400' : 'text-gray-600'}`} />
                  ))}
                </div>
                <blockquote className="italic text-gray-300 border-l-2 border-primary/50 pl-3">"{testimonial.quote}"</blockquote>
              </CardContent>
              <CardFooter className="flex justify-end gap-2 bg-gray-800/50 pt-4">
                <Button variant="outline" size="sm" onClick={() => handleEdit(testimonial)} className="text-primary border-primary hover:bg-primary/10 hover:text-primary">
                  <Edit className="h-4 w-4 mr-2" /> Edit
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructiveOutline" size="sm">
                      <Trash2 className="h-4 w-4 mr-2" /> Hapus
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-gray-800 border-gray-700 text-white">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
                      <AlertDialogDescription className="text-gray-400">
                        Tindakan ini tidak dapat diurungkan. Ini akan menghapus testimoni secara permanen.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="bg-gray-600 hover:bg-gray-500 border-none">Batal</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleDelete(testimonial.id)} className="bg-destructive hover:bg-destructive/90">Hapus</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[480px] bg-gray-800 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-primary">{currentTestimonial?.id ? 'Edit Testimoni' : 'Tambah Testimoni Baru'}</DialogTitle>
            <DialogDescription className="text-gray-400">
              Isi detail testimoni di bawah ini. Klik simpan jika sudah selesai.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="flex items-center gap-4">
              <div className="flex flex-col items-center gap-2 w-24">
                <img src={avatarPreview || `https://ui-avatars.com/api/?name=${currentTestimonial?.name || '?'}&background=1f2937&color=fff&size=96`} alt="Avatar Preview" className="w-24 h-24 rounded-full object-cover border-2 border-gray-600" />
                <Button size="sm" variant="outline" onClick={() => avatarInputRef.current?.click()} className="text-xs border-gray-500">
                    <ImagePlus className="w-3 h-3 mr-1" /> Ganti Foto
                </Button>
                <Input type="file" accept="image/*" ref={avatarInputRef} onChange={handleAvatarChange} className="hidden" />
              </div>
              <div className="flex-1 space-y-4">
                  <div>
                    <Label htmlFor="name">Nama</Label>
                    <Input id="name" value={currentTestimonial?.name || ''} onChange={(e) => setCurrentTestimonial({ ...currentTestimonial, name: e.target.value })} className="bg-gray-700 border-gray-600" />
                  </div>
                  <div>
                    <Label htmlFor="role">Jabatan/Peran</Label>
                    <Input id="role" value={currentTestimonial?.role || ''} onChange={(e) => setCurrentTestimonial({ ...currentTestimonial, role: e.target.value })} className="col-span-3 bg-gray-700 border-gray-600" />
                  </div>
              </div>
            </div>
            <div>
              <Label htmlFor="quote">Kutipan</Label>
              <Textarea id="quote" value={currentTestimonial?.quote || ''} onChange={(e) => setCurrentTestimonial({ ...currentTestimonial, quote: e.target.value })} className="bg-gray-700 border-gray-600" rows={4} />
            </div>
             <div>
              <Label>Rating Bintang</Label>
              <StarRatingInput rating={currentTestimonial?.stars || 5} setRating={(r) => setCurrentTestimonial({...currentTestimonial, stars: r})} />
            </div>
          </div>
          <DialogFooter>
             <Button variant="outline" onClick={() => setIsDialogOpen(false)} className="text-gray-300 border-gray-600 hover:bg-gray-700">Batal</Button>
            <Button type="submit" onClick={handleSave} className="gold-gradient text-black font-semibold">Simpan Perubahan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminTestimonialsPage;
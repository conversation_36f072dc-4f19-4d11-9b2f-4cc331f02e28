import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Save, Bot } from 'lucide-react';
import { motion } from 'framer-motion';
import { useContent } from '@/contexts/ContentContext.jsx';

const AdminWhatsAppManagementPage = () => {
  const { siteSettings, updateSiteSettings, isLoading } = useContent();
  const [whatsappSettings, setWhatsappSettings] = useState({
    primaryContactNumber: '',
    ctaMessage: '',
  });

  useEffect(() => {
    if (siteSettings && siteSettings.whatsapp) {
      setWhatsappSettings(siteSettings.whatsapp);
    }
  }, [siteSettings]);

  const handleInputChange = (field, value) => {
    setWhatsappSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveChanges = () => {
    updateSiteSettings({ ...siteSettings, whatsapp: whatsappSettings });
  };
  
  if (isLoading) {
    return <div className="text-center text-white py-10">Memuat pengaturan WhatsApp...</div>
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex items-center gap-4">
        <Bot size={32} className="text-primary" />
        <h1 className="text-3xl font-bold text-white tracking-tight">Manajemen WhatsApp</h1>
      </div>

      <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
        <CardHeader>
          <CardTitle>Pengaturan WhatsApp Global</CardTitle>
          <CardDescription className="text-gray-400">
            Atur nomor WhatsApp dan pesan default yang akan digunakan di seluruh tombol Call-to-Action (CTA) pada website Anda.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label htmlFor="primaryContactNumber" className="text-gray-300">
              Nomor WhatsApp Utama (dengan kode negara)
            </Label>
            <Input
              id="primaryContactNumber"
              value={whatsappSettings.primaryContactNumber || ''}
              onChange={(e) => handleInputChange('primaryContactNumber', e.target.value)}
              placeholder="Contoh: 6281234567890"
              className="bg-gray-700 border-gray-600 focus:border-primary"
            />
            <p className="text-xs text-gray-500 mt-1">
              Nomor ini akan menjadi tujuan utama untuk tombol-tombol "Hubungi via WhatsApp".
            </p>
          </div>

          <div>
            <Label htmlFor="ctaMessage" className="text-gray-300">
              Template Pesan Default (Pre-filled Message)
            </Label>
            <Textarea
              id="ctaMessage"
              value={whatsappSettings.ctaMessage || ''}
              onChange={(e) => handleInputChange('ctaMessage', e.target.value)}
              rows={4}
              placeholder="Contoh: Assalamualaikum, saya tertarik dengan layanan handling umrah Anda. Mohon informasinya."
              className="bg-gray-700 border-gray-600 focus:border-primary"
            />
             <p className="text-xs text-gray-500 mt-1">
              Pesan ini akan otomatis terisi ketika pengunjung mengklik tombol WhatsApp.
            </p>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSaveChanges} className="gold-gradient text-black font-semibold hover:opacity-90">
              <Save size={18} className="mr-2" /> Simpan Pengaturan
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AdminWhatsAppManagementPage;
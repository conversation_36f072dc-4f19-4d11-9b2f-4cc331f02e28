// Get base API URL from environment variables
const API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || 'https://api.placeholder.com';

/**
 * Generic API service for making HTTP requests
 * Provides a consistent interface for all API calls across the application
 */
class ApiService {
    constructor(baseURL = API_BASE_URL) {
        this.baseURL = baseURL;
    }

    /**
     * Make a generic API request
     * @param {string} endpoint - The API endpoint (e.g., '/orders', '/users/123')
     * @param {Object} options - Fetch options (method, headers, body, etc.)
     * @returns {Promise<any>} - The response data
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
            ...options,
        };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Handle empty responses (e.g., 204 No Content)
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return null;
        } catch (error) {
            console.error(`API request failed for ${endpoint}:`, error);
            throw error;
        }
    }

    /**
     * GET request
     * @param {string} endpoint - The API endpoint
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async get(endpoint, options = {}) {
        return this.request(endpoint, {
            method: 'GET',
            ...options,
        });
    }

    /**
     * POST request
     * @param {string} endpoint - The API endpoint
     * @param {any} data - The data to send in the request body
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async post(endpoint, data = null, options = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : null,
            ...options,
        });
    }

    /**
     * PUT request
     * @param {string} endpoint - The API endpoint
     * @param {any} data - The data to send in the request body
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async put(endpoint, data = null, options = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data ? JSON.stringify(data) : null,
            ...options,
        });
    }

    /**
     * PATCH request
     * @param {string} endpoint - The API endpoint
     * @param {any} data - The data to send in the request body
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async patch(endpoint, data = null, options = {}) {
        return this.request(endpoint, {
            method: 'PATCH',
            body: data ? JSON.stringify(data) : null,
            ...options,
        });
    }

    /**
     * DELETE request
     * @param {string} endpoint - The API endpoint
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async delete(endpoint, options = {}) {
        return this.request(endpoint, {
            method: 'DELETE',
            ...options,
        });
    }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// Export the class for creating custom instances if needed
export { ApiService };

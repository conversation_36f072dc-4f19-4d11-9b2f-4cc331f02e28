export const blogStorageService = {
  loadPosts: () => {
    try {
      const storedPosts = localStorage.getItem('adminBlogPosts');
      return storedPosts ? JSON.parse(storedPosts) : null;
    } catch (error) {
      console.error('Error loading posts from localStorage:', error);
      return null;
    }
  },

  savePosts: (posts) => {
    try {
      localStorage.setItem('adminBlogPosts', JSON.stringify(posts));
    } catch (error) {
      console.error('Error saving posts to localStorage:', error);
      throw error;
    }
  }
};
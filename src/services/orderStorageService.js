import { v4 as uuidv4 } from 'uuid';

// Get base API URL from environment variables
const API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || 'https://api.placeholder.com';

// Helper function to handle API requests
const apiRequest = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers,
        },
        ...options,
    };

    try {
        const response = await fetch(url, config);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error(`API request failed for ${endpoint}:`, error);
        throw error;
    }
};

const getOrders = async () => {
    try {
        const orders = await apiRequest('/orders');
        return orders || [];
    } catch (error) {
        console.error('Could not get orders from API', error);
        return [];
    }
};

const addOrder = async (formData, summary) => {
    try {
        const date = new Date();
        const newOrder = {
            id: `ORD-${date.getFullYear()}${(date.getMonth() + 1)
                .toString()
                .padStart(2, '0')}-${uuidv4().slice(0, 4).toUpperCase()}`,
            formData,
            summary,
            createdAt: date.toISOString(),
            status: 'Baru', // Initial status
        };

        const createdOrder = await apiRequest('/orders', {
            method: 'POST',
            body: JSON.stringify(newOrder),
        });

        return createdOrder;
    } catch (error) {
        console.error('Could not create order via API', error);
        throw error;
    }
};

const getOrderById = async (orderId) => {
    try {
        const order = await apiRequest(`/orders/${orderId}`);
        return order;
    } catch (error) {
        console.error(`Could not get order ${orderId} from API`, error);
        return null;
    }
};

const updateOrderStatus = async (orderId, status) => {
    try {
        const updatedOrder = await apiRequest(`/orders/${orderId}`, {
            method: 'PATCH',
            body: JSON.stringify({ status }),
        });

        return updatedOrder;
    } catch (error) {
        console.error(
            `Could not update order ${orderId} status via API`,
            error
        );
        throw new Error('Order not found or could not be updated');
    }
};

export const orderStorageService = {
    getOrders,
    addOrder,
    updateOrderStatus,
    getOrderById,
};

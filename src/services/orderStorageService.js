import { v4 as uuidv4 } from 'uuid';
import { apiService } from './apiService.js';

const getOrders = async () => {
    try {
        const orders = await apiService.get('/orders');
        return orders || [];
    } catch (error) {
        console.error('Could not get orders from API', error);
        return [];
    }
};

const addOrder = async (formData, summary) => {
    try {
        const date = new Date();
        const newOrder = {
            id: `ORD-${date.getFullYear()}${(date.getMonth() + 1)
                .toString()
                .padStart(2, '0')}-${uuidv4().slice(0, 4).toUpperCase()}`,
            formData,
            summary,
            createdAt: date.toISOString(),
            status: 'Baru', // Initial status
        };

        const createdOrder = await apiService.post('/orders', newOrder);

        return createdOrder;
    } catch (error) {
        console.error('Could not create order via API', error);
        throw error;
    }
};

const getOrderById = async (orderId) => {
    try {
        const order = await apiService.get(`/orders/${orderId}`);
        return order;
    } catch (error) {
        console.error(`Could not get order ${orderId} from API`, error);
        return null;
    }
};

const updateOrderStatus = async (orderId, status) => {
    try {
        const updatedOrder = await apiService.patch(`/orders/${orderId}`, {
            status,
        });

        return updatedOrder;
    } catch (error) {
        console.error(
            `Could not update order ${orderId} status via API`,
            error
        );
        throw new Error('Order not found or could not be updated');
    }
};

export const orderStorageService = {
    getOrders,
    addOrder,
    updateOrderStatus,
    getOrderById,
};

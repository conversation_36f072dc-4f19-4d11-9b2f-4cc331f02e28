import { v4 as uuidv4 } from 'uuid';

const ORDERS_KEY = 'orders';

const getOrders = () => {
    try {
        const ordersJson = localStorage.getItem(ORDERS_KEY);
        return ordersJson ? JSON.parse(ordersJson) : [];
    } catch (error) {
        console.error("Could not get orders from localStorage", error);
        return [];
    }
};

const saveOrders = (orders) => {
    try {
        localStorage.setItem(ORDERS_KEY, JSON.stringify(orders));
    } catch (error) {
        console.error("Could not save orders to localStorage", error);
    }
};

const addOrder = (formData, summary) => {
    const orders = getOrders();
    const date = new Date();
    const newOrder = {
        id: `ORD-${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}-${uuidv4().slice(0, 4).toUpperCase()}`,
        formData,
        summary,
        createdAt: date.toISOString(),
        status: 'Baru', // Initial status
    };
    orders.unshift(newOrder); // Add to the beginning of the array
    saveOrders(orders);
    return newOrder;
};

const getOrderById = (orderId) => {
    const orders = getOrders();
    return orders.find(order => order.id === orderId);
};

const updateOrderStatus = (orderId, status) => {
    const orders = getOrders();
    const orderIndex = orders.findIndex(o => o.id === orderId);
    if (orderIndex > -1) {
        orders[orderIndex].status = status;
        saveOrders(orders);
        return orders[orderIndex];
    }
    throw new Error("Order not found");
};

export const orderStorageService = {
    getOrders,
    addOrder,
    updateOrderStatus,
    getOrderById,
};
import { v4 as uuidv4 } from 'uuid';

const PARTNERS_KEY = 'partners';

const initialPartners = [
    { id: 'partner-1', name: '<PERSON><PERSON>', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/8b4aad63e6bcf6378e1fc17c3a51b018.png' },
    { id: 'partner-2', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> HBT Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0354b3ec9b12f6dce21f31c8a09e2389.png' },
    { id: 'partner-3', name: 'Alhamra Tours & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a718f251eab7757a6bbcaa09d3aa8a56.png' },
    { id: 'partner-4', name: '<PERSON><PERSON>', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/15299ceed57dc28831ad280c9dd6e5f8.png' },
    { id: 'partner-5', name: 'Amarcy Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/9307426587211e0cc03a9eaa416ca61d.png' },
    { id: 'partner-6', name: 'Arrahman Madinah Wisata', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/9dbd359f2325a34a35450c52e5871203.png' },
    { id: 'partner-7', name: 'Arretour Umroh, Hajj & Halal Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a87f350602a4477fad0cac3b0477bea7.png' },
    { id: 'partner-8', name: 'Ash Shafa', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/70b940bb83b29d4d8c9a4988aa201f81.png' },
    { id: 'partner-9', name: 'ATTMI', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a04addcf0a132ffdcc98c3d11498a6bf.png' },
    { id: 'partner-10', name: 'AtTiin Nabila Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/6b245d7e35e4a7e65963478e628f5c88.png' },
    { id: 'partner-11', name: 'Bihalal Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/bf67a4151182066350a32caea07defd0.png' },
    { id: 'partner-12', name: 'Bina Mandiri', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/02a636126ee575c627fd81a116022b08.png' },
    { id: 'partner-13', name: 'Diata Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/f53cf8de785373633ec62861b4e731e2.png' },
    { id: 'partner-14', name: 'Duta Mulia', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/f3473e4b5229c50c121e48cb3415d593.png' },
    { id: 'partner-15', name: 'Emira Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/72c0546f5aa2cbc8bc25dbc01e5d05be.png' },
    { id: 'partner-16', name: 'ER Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/db6fd43d5c34d3473fa10aa8daffbc0a.png' },
    { id: 'partner-17', name: 'El Safar Group Indowisata', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a6529bed66db473f257af960519d094a.png' },
    { id: 'partner-18', name: 'Gema Shafa Marwa', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/c38ea37e1e27495c21ce6d8a460b2019.png' },
    { id: 'partner-19', name: 'Global Wisata Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e58224cdb7d7cf48c8be1cf29a93e78e.png' },
    { id: 'partner-20', name: 'Jejak Imani', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/b8b87a107351068f333b557e593a5754.png' },
    { id: 'partner-21', name: 'Lawu', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/228bb73bad3c74067784526c10669d19.png' },
    { id: 'partner-22', name: 'Madanina Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/b5d75737253e26a0747b210a8707020e.png' },
    { id: 'partner-23', name: 'Mipro Azizan Wisata', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/3eb65de08c4c8b237b3e692f983248c4.png' },
    { id: 'partner-24', name: 'Bisa Umroh', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/2db8f5cf22417e51968e268fd3301f75.png' },
    { id: 'partner-25', name: 'Kiblat Kortasindo Perdana', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/7c0719c2ecb96fae274e03944e2506fc.png' },
    { id: 'partner-26', name: 'Lima Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a28230c32c4ec40e609f746e8e5fe5ef.png' },
    { id: 'partner-27', name: 'Fayyash Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/75490ea0715c16c7eebc91637e8d4d8d.png' },
    { id: 'partner-28', name: 'Mudaris Wisata', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/b46a226a08538f99b6ca9e66f315038a.png' },
    { id: 'partner-29', name: 'Raykha Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/74c36374aaff0106465840e69e5cf129.png' },
    { id: 'partner-30', name: 'Raml Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/16d66f5a82708642d1a44e430ece507c.png' },
    { id: 'partner-31', name: 'Ozman Tour and Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/280d2e186579fcbd389cb166c682a8ea.png' },
    { id: 'partner-32', name: 'Mudah Tours', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a04ce1c3b4fd35b8667f7b07edfdecd1.png' },
    { id: 'partner-33', name: 'PT. Selapan Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/cb7e5623edab87550f486ddb0413e541.png' },
    { id: 'partner-34', name: 'Sjava Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/d2aa663560e6eeeb39846392722d57fd.png' },
    { id: 'partner-35', name: 'SN 4444 Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/b33abdbcf10295d6903086b7bb4e1d16.png' },
    { id: 'partner-36', name: 'A-Plus Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/cf9fd38e01aabc9d1d6c4a75e482a2c6.png' },
    { id: 'partner-37', name: 'Pangeran Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/432e216d015a24ab9b3730f9272b145c.png' },
    { id: 'partner-38', name: 'Taiba Medina Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/cc572e88129058b1140c30b152595b87.png' },
    { id: 'partner-39', name: 'Raykha Halal Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e6a5cbd1c65718f91e6454624eedabed.png' },
    { id: 'partner-40', name: 'Tridaya Tour & Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/6e5ff5143e0f78bbab9f0ccb2baa2963.png' },
    { id: 'partner-41', name: 'PT. Mulia Mekah Madinah', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/7521fcbc77b6de0d2f228087e74b7ffd.png' },
    { id: 'partner-42', name: 'Svarga Tour', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/26f1355ca8cae6db7ff31e8e1bcb238f.png' },
    { id: 'partner-43', name: 'Tamasya Indah', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/376cf963a89d84a233acedafe2ee5f32.png' },
    { id: 'partner-44', name: 'Umaroh', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/947c07ace08d396badcb248ae3ff07a4.png' },
    { id: 'partner-45', name: 'UP Travel', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/9034651225594ea361155c624d601ada.png' },
    { id: 'partner-46', name: 'Urban Muslim Trip', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/132cecb9a57c061f2549a0a08fadfc21.png' },
    { id: 'partner-47', name: 'Zamzam Berkah Utama', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/5d412ffbaf9db89977495bc970f5ceb2.png' },
    { id: 'partner-48', name: 'Thoybah', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/68913dd764aa38ba73863a22f8419c4c.png' },
    { id: 'partner-49', name: 'Umrahype', logo: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/2919df453f2e53f5536fef631e2ed6bd.png' },
];

export const partnerStorageService = {
  loadPartners: () => {
    try {
      const storedPartners = localStorage.getItem(PARTNERS_KEY);
      if (storedPartners) {
        return JSON.parse(storedPartners);
      } else {
        localStorage.setItem(PARTNERS_KEY, JSON.stringify(initialPartners));
        return initialPartners;
      }
    } catch (error) {
      console.error('Error loading partners from localStorage:', error);
      return initialPartners;
    }
  },

  savePartners: (partners) => {
    try {
      localStorage.setItem(PARTNERS_KEY, JSON.stringify(partners));
    } catch (error) {
      console.error('Error saving partners to localStorage:', error);
      throw error;
    }
  },
};
/* Custom styles for React Quill to match the dark theme */

.quill-editor-override .ql-toolbar {
  @apply rounded-t-md border-gray-600 bg-gray-900 !important;
}

.quill-editor-override .ql-container {
  @apply rounded-b-md border-gray-600 bg-gray-700 text-gray-200 !important;
  min-height: 400px;
  font-size: 16px;
}

.quill-editor-override .ql-editor {
  @apply p-4;
}

.quill-editor-override .ql-editor.ql-blank::before {
  @apply text-gray-400 not-italic;
  left: 1rem;
  right: 1rem;
}

/* Toolbar icons color */
.quill-editor-override .ql-snow .ql-stroke {
  @apply stroke-current text-gray-300;
}
.quill-editor-override .ql-snow .ql-fill {
  @apply fill-current text-gray-300;
}
.quill-editor-override .ql-snow .ql-picker {
  @apply text-gray-300;
}

/* Toolbar active/hover states */
.quill-editor-override .ql-snow.ql-toolbar button:hover,
.quill-editor-override .ql-snow .ql-toolbar button:hover .ql-stroke,
.quill-editor-override .ql-snow .ql-toolbar button:hover .ql-fill,
.quill-editor-override .ql-snow .ql-toolbar button:focus,
.quill-editor-override .ql-snow .ql-toolbar button:focus .ql-stroke,
.quill-editor-override .ql-snow .ql-toolbar button:focus .ql-fill,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-label:hover,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-item:hover {
  @apply text-primary;
}

.quill-editor-override .ql-snow .ql-toolbar button.ql-active,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-label.ql-active,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  @apply text-primary;
}
.quill-editor-override .ql-snow .ql-toolbar button.ql-active .ql-stroke,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.quill-editor-override .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke {
  @apply stroke-current text-primary;
}

/* Dropdown panel styling */
.quill-editor-override .ql-snow .ql-picker-options {
  @apply bg-gray-800 border-gray-600;
}
.quill-editor-override .ql-snow .ql-picker-options .ql-picker-item {
  @apply text-gray-300;
}
.quill-editor-override .ql-snow .ql-picker-options .ql-picker-item:hover {
   @apply bg-primary/20 text-primary;
}
.quill-editor-override .ql-snow .ql-picker-options .ql-picker-item.ql-selected {
   @apply text-primary;
}

/* Tooltip styling for links */
.ql-tooltip {
  @apply bg-gray-800 border-gray-600 text-white rounded-md shadow-lg p-2;
  z-index: 100;
}
.ql-tooltip .ql-action::before {
  @apply text-gray-300;
}
.ql-tooltip input[type=text] {
  @apply bg-gray-700 border-gray-600 text-white rounded-sm px-2 py-1;
}
.ql-tooltip a.ql-action::after {
    content: 'Simpan';
    @apply ml-2 px-2 py-1 rounded-sm bg-primary text-black;
}
.ql-tooltip a.ql-remove::before {
    content: 'Hapus';
    @apply text-destructive;
}
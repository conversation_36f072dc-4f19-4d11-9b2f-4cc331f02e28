import { id as idLocale, enUS as enLocale, arSA as arLocale } from 'date-fns/locale';

const translations = {
  // Indonesian to English translations
  id_to_en: {
    "Paket Handling Service": "Handling Service Packages",
    "Pilih paket yang paling sesuai dengan kebutuhan dan anggaran Anda": "Choose the package that best suits your needs and budget",
    "Harga Transparan": "Transparent Pricing",
    "Tidak ada biaya tersembunyi": "No hidden fees",
    "Layanan Terjamin": "Guaranteed Service",
    "Semua layanan didukung oleh Muassasah resmi": "All services backed by official Muassasah",
    "Fleksibel": "Flexible",
    "Paket dapat disesuaikan": "Packages can be customized",
    "Pertanyaan Umum": "Frequently Asked Questions",
    "FAQ": "FAQ",
    "faq": "FAQ",
    "Temukan jawaban atas pertanyaan": "Find answers to frequently asked questions",
    "Hubungi Kami untuk Info Lebih Lanjut": "Contact Us for More Information",
    "Layanan Handling Umrah & Haji <PERSON>": "Our Comprehensive Umrah & Hajj Handling Services",
    "Blog & Artikel": "Blog & Articles",
    "Jelajahi wawasan, tips, dan berita terbaru dari kami.": "Explore insights, tips, and the latest news from us.",
    "Baca Selengkapnya": "Read More",
    "Artikel Terkait": "Related Articles",
    "Kembali ke Blog": "Back to Blog",
    "Tidak ada artikel yang ditemukan. Coba kata kunci atau filter lain.": "No articles found. Try different keywords or filters.",
    "Memuat artikel...": "Loading articles...",
    "Cari artikel...": "Search articles...",
    "Filter Kategori": "Filter Category",
    "Semua Kategori": "All Categories",
    "Tanya Layanan Ini": "Ask About This Service",
    "Pelajari Lebih Lanjut": "Learn More",
    "Tutup": "Close"
  },
  
  // Indonesian to Arabic translations
  id_to_ar: {
    "Paket Handling Service": "باقات خدمة المناولة",
    "Pilih paket yang paling sesuai dengan kebutuhan dan anggaran Anda": "اختر الباقة التي تناسب احتياجاتك وميزانيتك",
    "Harga Transparan": "أسعار شفافة",
    "Tidak ada biaya tersembunyi": "لا توجد رسوم مخفية",
    "Layanan Terjamin": "خدمة مضمونة",
    "Semua layanan didukung oleh Muassasah resmi": "جميع الخدمات مدعومة من مؤسسة رسمية",
    "Fleksibel": "مرن",
    "Paket dapat disesuaikan": "يمكن تخصيص الباقات",
    "Pertanyaan Umum": "الأسئلة الشائعة",
    "FAQ": "الأسئلة الشائعة",
    "faq": "الأسئلة الشائعة",
    "Temukan jawaban atas pertanyaan": "اعثر على إجابات للأسئلة الشائعة",
    "Hubungi Kami untuk Info Lebih Lanjut": "اتصل بنا لمزيد من المعلومات",
    "Layanan Handling Umrah & Haji Komprehensif Kami": "خدماتنا الشاملة في مناولة العمرة والحج",
    "Blog & Artikel": "المدونة والمقالات",
    "Jelajahi wawasan, tips, dan berita terbaru dari kami.": "اكتشف الرؤى والنصائح وآخر الأخبار منا.",
    "Baca Selengkapnya": "اقرأ المزيد",
    "Artikel Terkait": "مقالات ذات صلة",
    "Kembali ke Blog": "العودة إلى المدونة",
    "Tidak ada artikel yang ditemukan. Coba kata kunci atau filter lain.": "لم يتم العثور على مقالات. جرب كلمات رئيسية أو عوامل تصفية مختلفة.",
    "Memuat artikel...": "تحميل المقالات...",
    "Cari artikel...": "ابحث في المقالات...",
    "Filter Kategori": "تصفية حسب الفئة",
    "Semua Kategori": "جميع الفئات",
    "Tanya Layanan Ini": "اسأل عن هذه الخدمة",
    "Pelajari Lebih Lanjut": "تعلم المزيد",
    "Tutup": "إغلاق"
  }
};

const aiTranslate = (text, fromLang, toLang, key) => {
  return text; 
};

export const autoTranslate = (text, toLang, translationsContext = {}, key) => {
  if (!text) return text;
  if (translationsContext[key] && translationsContext[key][toLang]) {
      return translationsContext[key][toLang];
  }
  if (translationsContext[key] && translationsContext[key]['id']) {
      return translationsContext[key]['id'];
  }
  return text;
};

export const translateBlogPost = (post, targetLang, translationsContext) => {
  if (!post) return null;
  
  const translate = (key, fallback) => {
    const postKey = `${post.id}_${key}`;
    const generalKey = key;

    return autoTranslate(fallback, targetLang, translationsContext, postKey) || autoTranslate(fallback, targetLang, translationsContext, generalKey) || fallback;
  }
  
  return {
    ...post,
    title: translate('title', post.title),
    meta: {
      ...post.meta,
      description: translate('description', post.meta?.description || '')
    },
  };
};

export const translateBlogPosts = (posts, targetLang, translationsContext) => {
  if (!posts) return [];
  return posts.map(post => translateBlogPost(post, targetLang, translationsContext));
};

export const getLocale = (lang) => {
  switch(lang) {
    case 'en': return enLocale;
    case 'ar': return arLocale;
    default: return idLocale;
  }
}